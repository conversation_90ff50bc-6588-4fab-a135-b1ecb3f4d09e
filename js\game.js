// 3D桌球游戏主逻辑
class PoolGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.world = null;
        this.balls = [];
        this.table = null;
        this.cueStick = null;
        this.cueBall = null;
        
        this.isAiming = false;
        this.power = 0;
        this.maxPower = 100;
        this.powerIncreasing = true;
        
        this.score = 0;
        this.ballsLeft = 15;
        this.currentPlayer = 1;
        
        this.mouse = { x: 0, y: 0 };
        this.raycaster = new THREE.Raycaster();
        
        this.init();
    }
    
    init() {
        this.setupScene();
        this.setupPhysics();
        this.setupLighting();
        this.createTable();
        this.createBalls();
        this.createCueStick();
        this.setupControls();
        this.animate();
    }
    
    setupScene() {
        // 创建场景
        this.scene = new THREE.Scene();
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        
        // 获取Canvas并设置为2D渲染
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        // 设置2D渲染参数
        this.tableWidth = 800;
        this.tableHeight = 400;
        this.tableX = (this.canvas.width - this.tableWidth) / 2;
        this.tableY = (this.canvas.height - this.tableHeight) / 2;
        this.ballRadius = 12;
        this.camera.position.set(0, 8, 12);
        this.camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('gameCanvas'),
            antialias: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }
    
    setupPhysics() {
        this.world = new CANNON.World();
        this.world.gravity.set(0, -9.82, 0);
        this.world.broadphase = new CANNON.NaiveBroadphase();
        this.world.solver.iterations = 10;
        
        // 添加材质
        this.ballMaterial = new CANNON.Material('ball');
        this.tableMaterial = new CANNON.Material('table');
        
        // 设置接触材质
        const ballTableContact = new CANNON.ContactMaterial(
            this.ballMaterial,
            this.tableMaterial,
            {
                friction: 0.05,  // 降低摩擦力，让球滚得更远
                restitution: 0.8  // 增加弹性
            }
        );
        
        const ballBallContact = new CANNON.ContactMaterial(
            this.ballMaterial,
            this.ballMaterial,
            {
                friction: 0.05,   // 降低球与球之间的摩擦
                restitution: 0.95  // 增加球与球碰撞的弹性
            }
        );
        
        this.world.addContactMaterial(ballTableContact);
        this.world.addContactMaterial(ballBallContact);
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 20, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -20;
        directionalLight.shadow.camera.right = 20;
        directionalLight.shadow.camera.top = 20;
        directionalLight.shadow.camera.bottom = -20;
        this.scene.add(directionalLight);
        
        // 聚光灯
        const spotLight = new THREE.SpotLight(0xffffff, 0.5);
        spotLight.position.set(0, 15, 0);
        spotLight.castShadow = true;
        spotLight.angle = Math.PI / 4;
        spotLight.penumbra = 0.1;
        spotLight.decay = 2;
        spotLight.distance = 30;
        this.scene.add(spotLight);
    }
    
    createTable() {
        // 桌面
        const tableGeometry = new THREE.BoxGeometry(16, 0.5, 8);
        const tableMaterial = new THREE.MeshLambertMaterial({ color: 0x0d5016 });
        this.table = new THREE.Mesh(tableGeometry, tableMaterial);
        this.table.position.y = -0.25;
        this.table.receiveShadow = true;
        this.scene.add(this.table);
        
        // 物理桌面
        const tableShape = new CANNON.Box(new CANNON.Vec3(8, 0.25, 4));
        const tableBody = new CANNON.Body({ mass: 0, material: this.tableMaterial });
        tableBody.addShape(tableShape);
        tableBody.position.set(0, -0.25, 0);
        this.world.add(tableBody);
        
        // 桌边
        this.createTableBorders();
        
        // 球袋
        this.createPockets();
    }
    
    createTableBorders() {
        const borderMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        
        // 长边
        for (let i = 0; i < 2; i++) {
            const borderGeometry = new THREE.BoxGeometry(16, 1, 0.5);
            const border = new THREE.Mesh(borderGeometry, borderMaterial);
            border.position.set(0, 0.25, i === 0 ? 4.25 : -4.25);
            border.castShadow = true;
            this.scene.add(border);
            
            // 物理边界
            const borderShape = new CANNON.Box(new CANNON.Vec3(8, 0.5, 0.25));
            const borderBody = new CANNON.Body({ mass: 0, material: this.tableMaterial });
            borderBody.addShape(borderShape);
            borderBody.position.set(0, 0.25, i === 0 ? 4.25 : -4.25);
            this.world.add(borderBody);
        }
        
        // 短边
        for (let i = 0; i < 2; i++) {
            const borderGeometry = new THREE.BoxGeometry(0.5, 1, 8);
            const border = new THREE.Mesh(borderGeometry, borderMaterial);
            border.position.set(i === 0 ? 8.25 : -8.25, 0.25, 0);
            border.castShadow = true;
            this.scene.add(border);
            
            // 物理边界
            const borderShape = new CANNON.Box(new CANNON.Vec3(0.25, 0.5, 4));
            const borderBody = new CANNON.Body({ mass: 0, material: this.tableMaterial });
            borderBody.addShape(borderShape);
            borderBody.position.set(i === 0 ? 8.25 : -8.25, 0.25, 0);
            this.world.add(borderBody);
        }
    }
    
    createPockets() {
        const pocketPositions = [
            [-7.5, 0, -3.5], [7.5, 0, -3.5],  // 顶部角落
            [-7.5, 0, 3.5], [7.5, 0, 3.5],    // 底部角落
            [0, 0, -3.5], [0, 0, 3.5]          // 中间
        ];
        
        pocketPositions.forEach(pos => {
            const pocketGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.5, 16);
            const pocketMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
            const pocket = new THREE.Mesh(pocketGeometry, pocketMaterial);
            pocket.position.set(pos[0], pos[1], pos[2]);
            this.scene.add(pocket);
        });
    }
    
    createBalls() {
        // 白球（主球）
        this.cueBall = {
            position: { x: -4, y: 0.3, z: 0 },
            velocity: { x: 0, z: 0 },
            userData: { type: 'cue' }
        };
        this.balls.push(this.cueBall);
        
        // 摆放彩球（三角形排列）
        let ballIndex = 0;
        for (let row = 0; row < 5; row++) {
            for (let col = 0; col <= row; col++) {
                if (ballIndex >= 15) break;
                
                const x = 3 + row * 0.3 * 1.8;
                const z = (col - row / 2) * 0.3 * 2;
                
                const ball = {
                    position: { x: x, y: 0.3, z: z },
                    velocity: { x: 0, z: 0 },
                    userData: { type: 'target', number: ballIndex + 1 }
                };
                
                this.balls.push(ball);
                ballIndex++;
            }
        }
    }
    
    createCueStick() {
        // 2D版本不需要创建3D球杆，球杆在render2D中绘制
    }
    
    setupControls() {
        const canvas = document.getElementById('gameCanvas');
        
        canvas.addEventListener('mousemove', (event) => {
            const rect = canvas.getBoundingClientRect();
            this.mouse.x = event.clientX - rect.left;
            this.mouse.y = event.clientY - rect.top;
        });
        
        canvas.addEventListener('mousedown', (event) => {
            if (event.button === 0) { // 左键
                this.startAiming();
            }
        });
        
        canvas.addEventListener('mouseup', (event) => {
            if (event.button === 0 && this.isAiming) {
                this.shoot();
            }
        });
        
        window.addEventListener('resize', () => {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
            this.tableX = (this.canvas.width - this.tableWidth) / 2;
            this.tableY = (this.canvas.height - this.tableHeight) / 2;
        });
    }
    
    updateCueStick() {
        // 2D版本不需要更新3D球杆，球杆在render2D中绘制
    }
    
    startAiming() {
        this.isAiming = true;
        this.power = 0;
        this.powerIncreasing = true;
        this.updatePowerMeter();
    }
    
    updatePowerMeter() {
        if (!this.isAiming) return;
        
        if (this.powerIncreasing) {
            this.power += 2;
            if (this.power >= this.maxPower) {
                this.powerIncreasing = false;
            }
        } else {
            this.power -= 2;
            if (this.power <= 0) {
                this.powerIncreasing = true;
            }
        }
        
        const powerBar = document.getElementById('powerBar');
        powerBar.style.height = (this.power / this.maxPower * 100) + '%';
        
        requestAnimationFrame(() => this.updatePowerMeter());
    }
    
    shoot() {
        this.isAiming = false;
        
        if (!this.cueBall) return;
        
        const cueBallX = this.tableX + this.tableWidth/2 + this.cueBall.position.x * (this.tableWidth/16);
        const cueBallY = this.tableY + this.tableHeight/2 + this.cueBall.position.z * (this.tableHeight/8);
        
        const dx = this.mouse.x - cueBallX;
        const dy = this.mouse.y - cueBallY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length > 0) {
            const forceMultiplier = this.power * 0.3;
            this.cueBall.velocity.x = (dx / length) * forceMultiplier;
            this.cueBall.velocity.z = (dy / length) * forceMultiplier;
        }
        
        // 重置力量条
        const powerBar = document.getElementById('powerBar');
        powerBar.style.height = '0%';
    }
    
    checkPockets() {
        const pocketPositions = [
            {x: -8, z: -4}, {x: 8, z: -4},
            {x: -8, z: 4}, {x: 8, z: 4},
            {x: 0, z: -4}, {x: 0, z: 4}
        ];
        
        this.balls.forEach((ball, index) => {
            pocketPositions.forEach(pocketPos => {
                const dx = ball.position.x - pocketPos.x;
                const dz = ball.position.z - pocketPos.z;
                const distance = Math.sqrt(dx * dx + dz * dz);
                if (distance < 0.8) {
                    if (ball.userData.type === 'target') {
                        this.score += 10;
                        this.ballsLeft--;
                        this.updateUI();
                    }
                    
                    // 移除球
                    this.balls.splice(index, 1);
                    
                    if (ball.userData.type === 'cue') {
                        // 重新放置白球
                        setTimeout(() => this.resetCueBall(), 1000);
                    }
                }
            });
        });
    }
    
    resetCueBall() {
        this.cueBall = {
            position: { x: -4, y: 0.3, z: 0 },
            velocity: { x: 0, z: 0 },
            userData: { type: 'cue' }
        };
        this.balls.push(this.cueBall);
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('ballsLeft').textContent = this.ballsLeft;
        document.getElementById('currentPlayer').textContent = `玩家${this.currentPlayer}`;
        
        if (this.ballsLeft === 0) {
            alert('恭喜！游戏完成！');
        }
    }
    
    resetGame() {
        // 清除所有球
        this.balls = [];
        
        // 重置游戏状态
        this.score = 0;
        this.ballsLeft = 15;
        this.currentPlayer = 1;
        
        // 重新创建球
        this.createBalls();
        this.updateUI();
    }
    
    toggleCamera() {
        // 切换相机视角
        if (this.camera.position.y > 5) {
            this.camera.position.set(0, 2, 8);
        } else {
            this.camera.position.set(0, 8, 12);
        }
        this.camera.lookAt(0, 0, 0);
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        // 更新物理模拟（简化版）
        this.updatePhysics();
        
        // 检查球袋
        this.checkPockets();
        
        // 2D渲染
        this.render2D();
    }
    
    updatePhysics() {
        // 简化的物理更新
        this.balls.forEach(ball => {
            if (ball.velocity) {
                // 更新位置
                ball.position.x += ball.velocity.x;
                ball.position.z += ball.velocity.z;
                
                // 应用摩擦力
                ball.velocity.x *= 0.98;
                ball.velocity.z *= 0.98;
                
                // 边界检测
                const halfTable = this.tableWidth / 2 - this.ballRadius;
                const halfTableZ = this.tableHeight / 2 - this.ballRadius;
                
                if (Math.abs(ball.position.x) > halfTable) {
                    ball.position.x = Math.sign(ball.position.x) * halfTable;
                    ball.velocity.x *= -0.8;
                }
                
                if (Math.abs(ball.position.z) > halfTableZ) {
                    ball.position.z = Math.sign(ball.position.z) * halfTableZ;
                    ball.velocity.z *= -0.8;
                }
                
                // 停止条件
                if (Math.abs(ball.velocity.x) < 0.1 && Math.abs(ball.velocity.z) < 0.1) {
                    ball.velocity.x = 0;
                    ball.velocity.z = 0;
                }
            }
        });
        
        // 球与球碰撞检测
        for (let i = 0; i < this.balls.length; i++) {
            for (let j = i + 1; j < this.balls.length; j++) {
                const ball1 = this.balls[i];
                const ball2 = this.balls[j];
                
                const dx = ball1.position.x - ball2.position.x;
                const dz = ball1.position.z - ball2.position.z;
                const distance = Math.sqrt(dx * dx + dz * dz);
                
                if (distance < this.ballRadius * 2) {
                    // 简单的碰撞响应
                    const angle = Math.atan2(dz, dx);
                    const sin = Math.sin(angle);
                    const cos = Math.cos(angle);
                    
                    // 分离球
                    const overlap = this.ballRadius * 2 - distance;
                    ball1.position.x += cos * overlap * 0.5;
                    ball1.position.z += sin * overlap * 0.5;
                    ball2.position.x -= cos * overlap * 0.5;
                    ball2.position.z -= sin * overlap * 0.5;
                    
                    // 交换速度分量
                    if (ball1.velocity && ball2.velocity) {
                        const v1x = ball1.velocity.x * cos + ball1.velocity.z * sin;
                        const v1z = ball1.velocity.z * cos - ball1.velocity.x * sin;
                        const v2x = ball2.velocity.x * cos + ball2.velocity.z * sin;
                        const v2z = ball2.velocity.z * cos - ball2.velocity.x * sin;
                        
                        ball1.velocity.x = v2x * cos - v1z * sin;
                        ball1.velocity.z = v1z * cos + v2x * sin;
                        ball2.velocity.x = v1x * cos - v2z * sin;
                        ball2.velocity.z = v2z * cos + v1x * sin;
                    }
                }
            }
        }
    }
    
    render2D() {
        // 清除画布
        this.ctx.fillStyle = '#1a1a2e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制台球桌
        this.drawTable();
        
        // 绘制球
        this.balls.forEach(ball => this.drawBall(ball));
        
        // 绘制瞄准线和球杆
        if (this.isAiming && this.cueBall) {
            this.drawAimingLine();
            this.drawCueStick();
        }
        
        // 绘制力度条
        if (this.isAiming) {
            this.drawPowerMeter();
        }
    }
    
    drawTable() {
        // 桌面
        this.ctx.fillStyle = '#0d5016';
        this.ctx.fillRect(this.tableX, this.tableY, this.tableWidth, this.tableHeight);
        
        // 桌边
        this.ctx.strokeStyle = '#8B4513';
        this.ctx.lineWidth = 8;
        this.ctx.strokeRect(this.tableX - 4, this.tableY - 4, this.tableWidth + 8, this.tableHeight + 8);
        
        // 球袋
        const pocketRadius = 25;
        const pockets = [
            {x: this.tableX, y: this.tableY},
            {x: this.tableX + this.tableWidth, y: this.tableY},
            {x: this.tableX, y: this.tableY + this.tableHeight},
            {x: this.tableX + this.tableWidth, y: this.tableY + this.tableHeight},
            {x: this.tableX + this.tableWidth/2, y: this.tableY},
            {x: this.tableX + this.tableWidth/2, y: this.tableY + this.tableHeight}
        ];
        
        this.ctx.fillStyle = '#000';
        pockets.forEach(pocket => {
            this.ctx.beginPath();
            this.ctx.arc(pocket.x, pocket.y, pocketRadius, 0, Math.PI * 2);
            this.ctx.fill();
        });
    }
    
    drawBall(ball) {
        const screenX = this.tableX + this.tableWidth/2 + ball.position.x * (this.tableWidth/16);
        const screenY = this.tableY + this.tableHeight/2 + ball.position.z * (this.tableHeight/8);
        
        // 球的阴影
        this.ctx.fillStyle = 'rgba(0,0,0,0.3)';
        this.ctx.beginPath();
        this.ctx.arc(screenX + 2, screenY + 2, this.ballRadius, 0, Math.PI * 2);
        this.ctx.fill();
        
        // 球体
        const gradient = this.ctx.createRadialGradient(
            screenX - this.ballRadius/3, screenY - this.ballRadius/3, 0,
            screenX, screenY, this.ballRadius
        );
        
        if (ball.userData && ball.userData.type === 'cue') {
            gradient.addColorStop(0, '#ffffff');
            gradient.addColorStop(1, '#e0e0e0');
        } else {
            const colors = ['#ff0000', '#0000ff', '#ffff00', '#ff8000', '#800080', '#008000', '#800000', '#000080'];
            const colorIndex = (ball.userData && ball.userData.number) ? (ball.userData.number - 1) % colors.length : 0;
            gradient.addColorStop(0, colors[colorIndex]);
            gradient.addColorStop(1, '#333');
        }
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(screenX, screenY, this.ballRadius, 0, Math.PI * 2);
        this.ctx.fill();
        
        // 球的边框
        this.ctx.strokeStyle = '#333';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
        
        // 球号
        if (ball.userData && ball.userData.number && ball.userData.type !== 'cue') {
            this.ctx.fillStyle = '#fff';
            this.ctx.font = '10px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(ball.userData.number, screenX, screenY + 3);
        }
    }
    
    drawAimingLine() {
        if (!this.cueBall) return;
        
        const cueBallX = this.tableX + this.tableWidth/2 + this.cueBall.position.x * (this.tableWidth/16);
        const cueBallY = this.tableY + this.tableHeight/2 + this.cueBall.position.z * (this.tableHeight/8);
        
        const dx = this.mouse.x - cueBallX;
        const dy = this.mouse.y - cueBallY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length > 0) {
            const aimLength = Math.min(length, 150);
            const endX = cueBallX + (dx / length) * aimLength;
            const endY = cueBallY + (dy / length) * aimLength;
            
            this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([5, 5]);
            this.ctx.beginPath();
            this.ctx.moveTo(cueBallX, cueBallY);
            this.ctx.lineTo(endX, endY);
            this.ctx.stroke();
            this.ctx.setLineDash([]);
        }
    }
    
    drawCueStick() {
        if (!this.cueBall) return;
        
        const cueBallX = this.tableX + this.tableWidth/2 + this.cueBall.position.x * (this.tableWidth/16);
        const cueBallY = this.tableY + this.tableHeight/2 + this.cueBall.position.z * (this.tableHeight/8);
        
        const dx = this.mouse.x - cueBallX;
        const dy = this.mouse.y - cueBallY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length > 0) {
            const angle = Math.atan2(dy, dx);
            const stickLength = 120;
            const stickDistance = 80;
            
            const stickStartX = cueBallX - Math.cos(angle) * stickDistance;
            const stickStartY = cueBallY - Math.sin(angle) * stickDistance;
            const stickEndX = stickStartX - Math.cos(angle) * stickLength;
            const stickEndY = stickStartY - Math.sin(angle) * stickLength;
            
            // 球杆主体
            const gradient = this.ctx.createLinearGradient(stickStartX, stickStartY, stickEndX, stickEndY);
            gradient.addColorStop(0, '#8B4513');
            gradient.addColorStop(0.8, '#D2691E');
            gradient.addColorStop(1, '#A0522D');
            
            this.ctx.strokeStyle = gradient;
            this.ctx.lineWidth = 8;
            this.ctx.beginPath();
            this.ctx.moveTo(stickStartX, stickStartY);
            this.ctx.lineTo(stickEndX, stickEndY);
            this.ctx.stroke();
            
            // 球杆头
            this.ctx.strokeStyle = '#444';
            this.ctx.lineWidth = 6;
            this.ctx.beginPath();
            this.ctx.moveTo(stickStartX, stickStartY);
            this.ctx.lineTo(stickStartX - Math.cos(angle) * 15, stickStartY - Math.sin(angle) * 15);
            this.ctx.stroke();
        }
    }
    
    drawPowerMeter() {
        const meterX = this.canvas.width - 50;
        const meterY = this.canvas.height / 2 - 100;
        const meterHeight = 200;
        const meterWidth = 20;
        
        // 背景
        this.ctx.fillStyle = 'rgba(0,0,0,0.5)';
        this.ctx.fillRect(meterX, meterY, meterWidth, meterHeight);
        
        // 力度条
        const powerHeight = (this.power / this.maxPower) * meterHeight;
        const gradient = this.ctx.createLinearGradient(0, meterY + meterHeight, 0, meterY);
        gradient.addColorStop(0, '#00ff00');
        gradient.addColorStop(0.5, '#ffff00');
        gradient.addColorStop(1, '#ff0000');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(meterX, meterY + meterHeight - powerHeight, meterWidth, powerHeight);
        
        // 边框
        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(meterX, meterY, meterWidth, meterHeight);
    }
}

// 游戏类已定义，由HTML中的错误处理代码负责初始化