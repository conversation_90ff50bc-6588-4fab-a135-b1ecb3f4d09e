<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D桌球游戏 - 离线版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            cursor: crosshair;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 100;
        }

        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        #powerMeter {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid white;
            border-radius: 10px;
            z-index: 100;
        }

        #powerBar {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(to top, #00ff00, #ffff00, #ff0000);
            border-radius: 8px;
            transition: height 0.1s;
        }

        #loadingMessage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }

        #errorMessage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff6b6b;
            font-size: 20px;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 10px;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <div id="loadingMessage">
        <div>正在加载3D桌球游戏...</div>
        <div style="margin-top: 20px; font-size: 16px;">如果长时间无响应，请检查网络连接</div>
    </div>

    <div id="gameContainer" style="display: none;">
        <canvas id="gameCanvas"></canvas>

        <div id="ui">
            <div>得分: <span id="score">0</span></div>
            <div>剩余球数: <span id="ballsLeft">15</span></div>
            <div>当前玩家: <span id="currentPlayer">玩家1</span></div>
        </div>


        <div id="controls">
            <div>使用鼠标瞄准，点击击球</div>
            <div style="font-size: 14px; margin-top: 5px;">
                按 H 键切换专业瞄准辅助
            </div>
            <button class="button" onclick="game.resetGame()">重新开始</button>
        </div>

        <div id="powerMeter">
            <div id="powerBar" style="height: 0%;"></div>
        </div>
    </div>

    <script>
        // 简化版3D桌球游戏（不依赖外部库）
        class SimplePoolGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;

                this.balls = [];
                this.cueBall = null;
                this.isAiming = false;
                this.power = 0;
                this.maxPower = 100;
                this.powerIncreasing = true;

                this.score = 0;
                this.ballsLeft = 15;

                this.mouse = { x: 0, y: 0 };
                this.aimLine = { start: null, end: null };
                this.cueStick = { x: 0, y: 0, angle: 0, length: 120, visible: false };

                // 专业瞄准系统
                this.aimingSystem = {
                    targetBall: null,
                    hitPoint: null,
                    pocketTarget: null,
                    showHelper: true
                };

                // 球袋位置
                this.pockets = [
                    {x: 50, y: 50, id: 'top-left'},
                    {x: this.canvas.width - 50, y: 50, id: 'top-right'},
                    {x: 50, y: this.canvas.height - 50, id: 'bottom-left'},
                    {x: this.canvas.width - 50, y: this.canvas.height - 50, id: 'bottom-right'},
                    {x: this.canvas.width / 2, y: 50, id: 'top-center'},
                    {x: this.canvas.width / 2, y: this.canvas.height - 50, id: 'bottom-center'}
                ];

                this.init();
            }

            init() {
                this.createBalls();
                this.setupControls();
                this.animate();
                this.updateUI();
            }

            createBalls() {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                const ballRadius = 15;

                // 白球（主球）
                this.cueBall = {
                    x: centerX - 200,
                    y: centerY,
                    vx: 0,
                    vy: 0,
                    radius: ballRadius,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);

                // 彩球（三角形排列）
                const colors = [
                    '#ff0000', '#0000ff', '#ffff00', '#800080', '#ffa500',
                    '#008000', '#800000', '#000000', '#ff69b4', '#00ffff',
                    '#ff1493', '#32cd32', '#8b4513', '#4169e1', '#ff6347'
                ];

                let ballIndex = 0;
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        if (ballIndex >= 15) break;

                        const x = centerX + 100 + row * ballRadius * 1.8;
                        const y = centerY + (col - row / 2) * ballRadius * 2;

                        this.balls.push({
                            x: x,
                            y: y,
                            vx: 0,
                            vy: 0,
                            radius: ballRadius,
                            color: colors[ballIndex],
                            type: 'target',
                            id: ballIndex + 1
                        });
                        ballIndex++;
                    }
                }
            }

            setupControls() {
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.mouse.x = e.clientX - rect.left;
                    this.mouse.y = e.clientY - rect.top;

                    if (!this.isAiming) {
                        this.updateAimLine();
                    }
                });

                this.canvas.addEventListener('mousedown', (e) => {
                    if (e.button === 0) {
                        this.startAiming();
                    }
                });

                this.canvas.addEventListener('mouseup', (e) => {
                    if (e.button === 0 && this.isAiming) {
                        this.shoot();
                    }
                });

                window.addEventListener('resize', () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                    // 更新球袋位置
                    this.pockets = [
                        {x: 50, y: 50, id: 'top-left'},
                        {x: this.canvas.width - 50, y: 50, id: 'top-right'},
                        {x: 50, y: this.canvas.height - 50, id: 'bottom-left'},
                        {x: this.canvas.width - 50, y: this.canvas.height - 50, id: 'bottom-right'},
                        {x: this.canvas.width / 2, y: 50, id: 'top-center'},
                        {x: this.canvas.width / 2, y: this.canvas.height - 50, id: 'bottom-center'}
                    ];
                });

                // 键盘控制
                window.addEventListener('keydown', (e) => {
                    if (e.key.toLowerCase() === 'h') {
                        this.aimingSystem.showHelper = !this.aimingSystem.showHelper;
                    }
                });
            }

            updateAimLine() {
                if (!this.cueBall) return;

                const dx = this.mouse.x - this.cueBall.x;
                const dy = this.mouse.y - this.cueBall.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > 0) {
                    const normalizedX = dx / distance;
                    const normalizedY = dy / distance;

                    // 更新球杆位置
                    this.cueStick = {
                        angle: Math.atan2(dy, dx),
                        x: this.cueBall.x - normalizedX * 80,
                        y: this.cueBall.y - normalizedY * 80,
                        length: 120,
                        visible: true
                    };

                    // 更新瞄准系统
                    this.updateAimingSystem(normalizedX, normalizedY);

                    this.aimLine.start = {
                        x: this.cueBall.x - normalizedX * 30,
                        y: this.cueBall.y - normalizedY * 30
                    };

                    this.aimLine.end = {
                        x: this.cueBall.x + normalizedX * 150,
                        y: this.cueBall.y + normalizedY * 150
                    };
                }
            }

            updateAimingSystem(dirX, dirY) {
                // 重置瞄准系统
                this.aimingSystem.targetBall = null;
                this.aimingSystem.hitPoint = null;
                this.aimingSystem.pocketTarget = null;

                // 寻找瞄准线上的第一个球
                let closestBall = null;
                let closestDistance = Infinity;

                this.balls.forEach(ball => {
                    if (ball === this.cueBall) return;

                    // 计算射线与球的交点
                    const toBall = {
                        x: ball.x - this.cueBall.x,
                        y: ball.y - this.cueBall.y
                    };

                    // 投影到射线方向
                    const projection = toBall.x * dirX + toBall.y * dirY;
                    if (projection <= 0) return; // 球在射线后方

                    // 计算最近点
                    const closestPoint = {
                        x: this.cueBall.x + dirX * projection,
                        y: this.cueBall.y + dirY * projection
                    };

                    // 计算距离
                    const distance = Math.sqrt(
                        Math.pow(closestPoint.x - ball.x, 2) +
                        Math.pow(closestPoint.y - ball.y, 2)
                    );

                    // 检查是否碰撞
                    if (distance <= ball.radius * 2 && projection < closestDistance) {
                        closestDistance = projection;
                        closestBall = ball;

                        this.aimingSystem.hitPoint = {
                            x: ball.x - dirX * ball.radius,
                            y: ball.y - dirY * ball.radius
                        };
                    }
                });

                if (closestBall) {
                    this.aimingSystem.targetBall = closestBall;
                    this.findBestPocket(closestBall);
                }
            }

            findBestPocket(targetBall) {
                let bestPocket = null;
                let bestScore = -1;

                this.pockets.forEach(pocket => {
                    const toPocket = {
                        x: pocket.x - targetBall.x,
                        y: pocket.y - targetBall.y
                    };
                    const distance = Math.sqrt(toPocket.x * toPocket.x + toPocket.y * toPocket.y);

                    if (distance > 0) {
                        const normalizedToPocket = {
                            x: toPocket.x / distance,
                            y: toPocket.y / distance
                        };

                        const ballDir = {
                            x: targetBall.x - this.cueBall.x,
                            y: targetBall.y - this.cueBall.y
                        };
                        const ballDirLength = Math.sqrt(ballDir.x * ballDir.x + ballDir.y * ballDir.y);

                        if (ballDirLength > 0) {
                            const normalizedBallDir = {
                                x: ballDir.x / ballDirLength,
                                y: ballDir.y / ballDirLength
                            };

                            const dotProduct = normalizedBallDir.x * normalizedToPocket.x +
                                             normalizedBallDir.y * normalizedToPocket.y;
                            const distanceFactor = 1 / (1 + distance * 0.001);
                            const score = dotProduct * distanceFactor;

                            if (score > bestScore) {
                                bestScore = score;
                                bestPocket = pocket;
                            }
                        }
                    }
                });

                if (bestScore > 0.3) {
                    this.aimingSystem.pocketTarget = bestPocket;
                }
            }

            startAiming() {
                this.isAiming = true;
                this.power = 0;
                this.powerIncreasing = true;
                this.updatePowerMeter();
            }

            updatePowerMeter() {
                if (!this.isAiming) return;

                if (this.powerIncreasing) {
                    this.power += 2;
                    if (this.power >= this.maxPower) {
                        this.powerIncreasing = false;
                    }
                } else {
                    this.power -= 2;
                    if (this.power <= 0) {
                        this.powerIncreasing = true;
                    }
                }

                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = (this.power / this.maxPower * 100) + '%';

                requestAnimationFrame(() => this.updatePowerMeter());
            }

            shoot() {
                this.isAiming = false;

                const dx = this.mouse.x - this.cueBall.x;
                const dy = this.mouse.y - this.cueBall.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > 0) {
                    const force = this.power * 0.6;  // 调整击球力度
                    this.cueBall.vx = (dx / distance) * force;
                    this.cueBall.vy = (dy / distance) * force;
                }

                // 隐藏球杆
                this.cueStick.visible = false;

                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = '0%';
            }

            updatePhysics() {
                let anyBallMoving = false;

                // 更新球的位置
                this.balls.forEach(ball => {
                    ball.x += ball.vx;
                    ball.y += ball.vy;

                    // 摩擦力（降低阻力，让球滚得更远）
                    ball.vx *= 0.992;
                    ball.vy *= 0.992;

                    // 边界碰撞
                    if (ball.x - ball.radius < 50 || ball.x + ball.radius > this.canvas.width - 50) {
                        ball.vx = -ball.vx * 0.8;
                        ball.x = Math.max(50 + ball.radius, Math.min(this.canvas.width - 50 - ball.radius, ball.x));
                    }
                    if (ball.y - ball.radius < 50 || ball.y + ball.radius > this.canvas.height - 50) {
                        ball.vy = -ball.vy * 0.8;
                        ball.y = Math.max(50 + ball.radius, Math.min(this.canvas.height - 50 - ball.radius, ball.y));
                    }

                    // 检查球是否还在运动
                    if (Math.abs(ball.vx) > 0.3 || Math.abs(ball.vy) > 0.3) {
                        anyBallMoving = true;
                    } else {
                        ball.vx = 0;
                        ball.vy = 0;
                    }
                });

                // 如果有球在运动，隐藏球杆；如果都停止了，显示球杆
                if (anyBallMoving) {
                    this.cueStick.visible = false;
                } else if (!this.isAiming) {
                    this.updateAimLine();
                }

                // 球与球碰撞
                for (let i = 0; i < this.balls.length; i++) {
                    for (let j = i + 1; j < this.balls.length; j++) {
                        const ball1 = this.balls[i];
                        const ball2 = this.balls[j];

                        const dx = ball2.x - ball1.x;
                        const dy = ball2.y - ball1.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < ball1.radius + ball2.radius) {
                            // 简单的弹性碰撞
                            const angle = Math.atan2(dy, dx);
                            const sin = Math.sin(angle);
                            const cos = Math.cos(angle);

                            // 交换速度分量
                            const vx1 = ball1.vx * cos + ball1.vy * sin;
                            const vy1 = ball1.vy * cos - ball1.vx * sin;
                            const vx2 = ball2.vx * cos + ball2.vy * sin;
                            const vy2 = ball2.vy * cos - ball2.vx * sin;

                            ball1.vx = vx2 * cos - vy1 * sin;
                            ball1.vy = vy1 * cos + vx2 * sin;
                            ball2.vx = vx1 * cos - vy2 * sin;
                            ball2.vy = vy2 * cos + vx1 * sin;

                            // 分离球体
                            const overlap = ball1.radius + ball2.radius - distance;
                            const separateX = (dx / distance) * overlap * 0.5;
                            const separateY = (dy / distance) * overlap * 0.5;

                            ball1.x -= separateX;
                            ball1.y -= separateY;
                            ball2.x += separateX;
                            ball2.y += separateY;
                        }
                    }
                }
            }

            render() {
                // 清空画布
                this.ctx.fillStyle = '#0d5016';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // 绘制桌边
                this.ctx.strokeStyle = '#8B4513';
                this.ctx.lineWidth = 10;
                this.ctx.strokeRect(45, 45, this.canvas.width - 90, this.canvas.height - 90);

                // 绘制球袋
                this.ctx.fillStyle = '#000000';
                const pockets = [
                    [50, 50], [this.canvas.width - 50, 50],
                    [50, this.canvas.height - 50], [this.canvas.width - 50, this.canvas.height - 50],
                    [this.canvas.width / 2, 50], [this.canvas.width / 2, this.canvas.height - 50]
                ];

                pockets.forEach(pocket => {
                    this.ctx.beginPath();
                    this.ctx.arc(pocket[0], pocket[1], 25, 0, Math.PI * 2);
                    this.ctx.fill();
                });

                // 绘制球杆
                if (this.cueStick.visible) {
                    this.drawCueStick();
                }

                // 绘制专业瞄准系统
                if (!this.isAiming && this.aimingSystem.showHelper) {
                    this.drawProfessionalAiming();
                } else if (!this.isAiming) {
                    // 简单瞄准线
                    if (this.aimLine.start && this.aimLine.end) {
                        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                        this.ctx.lineWidth = 2;
                        this.ctx.setLineDash([8, 4]);
                        this.ctx.beginPath();
                        this.ctx.moveTo(this.aimLine.start.x, this.aimLine.start.y);
                        this.ctx.lineTo(this.aimLine.end.x, this.aimLine.end.y);
                        this.ctx.stroke();
                        this.ctx.setLineDash([]);
                    }
                }

                // 绘制瞄准辅助（蓄力时）
                if (this.isAiming) {
                    this.drawAimingHelper();
                }

                // 绘制球
                this.balls.forEach(ball => {
                    this.ctx.fillStyle = ball.color;
                    this.ctx.beginPath();
                    this.ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
                    this.ctx.fill();

                    // 球的边框
                    this.ctx.strokeStyle = '#333333';
                    this.ctx.lineWidth = 1;
                    this.ctx.stroke();

                    // 球号
                    if (ball.type === 'target') {
                        this.ctx.fillStyle = '#ffffff';
                        this.ctx.font = '12px Arial';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillText(ball.id.toString(), ball.x, ball.y + 4);
                    }
                });
            }

            drawCueStick() {
                const ctx = this.ctx;
                const stick = this.cueStick;

                ctx.save();
                ctx.translate(stick.x, stick.y);
                ctx.rotate(stick.angle);

                // 杆身主体（木质）
                const gradient1 = ctx.createLinearGradient(0, -4, 0, 4);
                gradient1.addColorStop(0, '#D2691E');
                gradient1.addColorStop(0.5, '#CD853F');
                gradient1.addColorStop(1, '#A0522D');

                ctx.fillStyle = gradient1;
                ctx.fillRect(0, -4, stick.length * 0.8, 8);

                // 杆头（金属）
                ctx.fillStyle = '#2F4F4F';
                ctx.fillRect(stick.length * 0.8, -3, stick.length * 0.1, 6);

                // 握把（皮质）
                const gradient2 = ctx.createLinearGradient(0, -5, 0, 5);
                gradient2.addColorStop(0, '#654321');
                gradient2.addColorStop(0.5, '#8B4513');
                gradient2.addColorStop(1, '#654321');

                ctx.fillStyle = gradient2;
                ctx.fillRect(-stick.length * 0.3, -5, stick.length * 0.25, 10);

                // 握把装饰环
                ctx.fillStyle = '#8B4513';
                for (let i = 0; i < 3; i++) {
                    const x = -stick.length * 0.2 + i * 8;
                    ctx.fillRect(x, -5, 2, 10);
                }

                // 杆尾配重
                ctx.fillStyle = '#8B0000';
                ctx.fillRect(-stick.length * 0.35, -4, stick.length * 0.05, 8);

                // 木纹效果
                ctx.strokeStyle = '#A0522D';
                ctx.lineWidth = 0.5;
                for (let i = 0; i < 5; i++) {
                    const x = i * (stick.length * 0.8 / 5);
                    ctx.beginPath();
                    ctx.moveTo(x, -3);
                    ctx.lineTo(x, 3);
                    ctx.stroke();
                }

                // 球杆边框
                ctx.strokeStyle = '#654321';
                ctx.lineWidth = 1;
                ctx.strokeRect(-stick.length * 0.35, -5, stick.length * 1.25, 10);

                ctx.restore();
            }

            drawProfessionalAiming() {
                if (!this.cueBall) return;

                // 1. 绘制主瞄准线
                if (this.aimLine.start && this.aimLine.end) {
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                    this.ctx.lineWidth = 3;
                    this.ctx.setLineDash([]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.aimLine.start.x, this.aimLine.start.y);

                    if (this.aimingSystem.targetBall) {
                        // 线到目标球
                        this.ctx.lineTo(this.aimingSystem.targetBall.x, this.aimingSystem.targetBall.y);
                    } else {
                        // 线到鼠标位置
                        this.ctx.lineTo(this.aimLine.end.x, this.aimLine.end.y);
                    }
                    this.ctx.stroke();
                }

                // 2. 高亮目标球
                if (this.aimingSystem.targetBall) {
                    const ball = this.aimingSystem.targetBall;

                    // 目标球光环
                    this.ctx.strokeStyle = 'rgba(255, 255, 0, 0.8)';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(ball.x, ball.y, ball.radius + 5, 0, Math.PI * 2);
                    this.ctx.stroke();

                    // 撞击点标记
                    if (this.aimingSystem.hitPoint) {
                        this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                        this.ctx.beginPath();
                        this.ctx.arc(this.aimingSystem.hitPoint.x, this.aimingSystem.hitPoint.y, 4, 0, Math.PI * 2);
                        this.ctx.fill();
                    }

                    // 虚拟球位置（白球撞击时的位置）
                    const dirToBall = {
                        x: ball.x - this.cueBall.x,
                        y: ball.y - this.cueBall.y
                    };
                    const distToBall = Math.sqrt(dirToBall.x * dirToBall.x + dirToBall.y * dirToBall.y);

                    if (distToBall > 0) {
                        const normalizedDir = {
                            x: dirToBall.x / distToBall,
                            y: dirToBall.y / distToBall
                        };

                        const ghostX = ball.x - normalizedDir.x * (this.cueBall.radius * 2);
                        const ghostY = ball.y - normalizedDir.y * (this.cueBall.radius * 2);

                        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                        this.ctx.lineWidth = 2;
                        this.ctx.setLineDash([4, 4]);
                        this.ctx.beginPath();
                        this.ctx.arc(ghostX, ghostY, this.cueBall.radius, 0, Math.PI * 2);
                        this.ctx.stroke();
                        this.ctx.setLineDash([]);
                    }
                }

                // 3. 绘制进袋路径
                if (this.aimingSystem.targetBall && this.aimingSystem.pocketTarget) {
                    const ball = this.aimingSystem.targetBall;
                    const pocket = this.aimingSystem.pocketTarget;

                    // 目标球到球袋的路径
                    this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.6)';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([6, 6]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(ball.x, ball.y);
                    this.ctx.lineTo(pocket.x, pocket.y);
                    this.ctx.stroke();
                    this.ctx.setLineDash([]);

                    // 球袋高亮
                    this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.8)';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(pocket.x, pocket.y, 25, 0, Math.PI * 2);
                    this.ctx.stroke();
                }

                // 4. 瞄准精度指示器
                if (!this.aimingSystem.targetBall && this.aimLine.end) {
                    // 自由瞄准模式
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    this.ctx.beginPath();
                    this.ctx.arc(this.aimLine.end.x, this.aimLine.end.y, 6, 0, Math.PI * 2);
                    this.ctx.fill();

                    // 十字准星
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.aimLine.end.x - 10, this.aimLine.end.y);
                    this.ctx.lineTo(this.aimLine.end.x + 10, this.aimLine.end.y);
                    this.ctx.moveTo(this.aimLine.end.x, this.aimLine.end.y - 10);
                    this.ctx.lineTo(this.aimLine.end.x, this.aimLine.end.y + 10);
                    this.ctx.stroke();
                }
            }

            drawAimingHelper() {
                if (!this.cueBall) return;

                // 力量指示器（围绕白球的圆圈）
                const powerRadius = 20 + (this.power / this.maxPower) * 30;
                this.ctx.strokeStyle = `rgba(255, ${255 - this.power * 2}, 0, 0.8)`;
                this.ctx.lineWidth = 3;
                this.ctx.beginPath();
                this.ctx.arc(this.cueBall.x, this.cueBall.y, powerRadius, 0, Math.PI * 2);
                this.ctx.stroke();

                // 预测轨迹
                const dx = this.mouse.x - this.cueBall.x;
                const dy = this.mouse.y - this.cueBall.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > 0) {
                    const force = this.power * 0.6;
                    const normalizedX = dx / distance;
                    const normalizedY = dy / distance;

                    let predX = this.cueBall.x;
                    let predY = this.cueBall.y;
                    let predVx = normalizedX * force;
                    let predVy = normalizedY * force;

                    this.ctx.strokeStyle = 'rgba(255, 255, 0, 0.5)';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([3, 3]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(predX, predY);

                    // 模拟轨迹
                    for (let i = 0; i < 50; i++) {
                        predX += predVx * 2;
                        predY += predVy * 2;
                        predVx *= 0.95;
                        predVy *= 0.95;

                        // 边界检测
                        if (predX < 50 || predX > this.canvas.width - 50 ||
                            predY < 50 || predY > this.canvas.height - 50) break;
                        if (Math.abs(predVx) < 1 && Math.abs(predVy) < 1) break;

                        this.ctx.lineTo(predX, predY);
                    }

                    this.ctx.stroke();
                    this.ctx.setLineDash([]);
                }
            }

            checkPockets() {
                const pockets = [
                    [50, 50], [this.canvas.width - 50, 50],
                    [50, this.canvas.height - 50], [this.canvas.width - 50, this.canvas.height - 50],
                    [this.canvas.width / 2, 50], [this.canvas.width / 2, this.canvas.height - 50]
                ];

                this.balls = this.balls.filter(ball => {
                    for (let pocket of pockets) {
                        const dx = ball.x - pocket[0];
                        const dy = ball.y - pocket[1];
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 25) {
                            if (ball.type === 'target') {
                                this.score += 10;
                                this.ballsLeft--;
                                this.updateUI();
                            } else if (ball.type === 'cue') {
                                // 重新放置白球
                                setTimeout(() => this.resetCueBall(), 1000);
                            }
                            return false; // 移除球
                        }
                    }
                    return true; // 保留球
                });
            }

            resetCueBall() {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;

                this.cueBall = {
                    x: centerX - 200,
                    y: centerY,
                    vx: 0,
                    vy: 0,
                    radius: 15,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);
            }

            updateUI() {
                document.getElementById('score').textContent = this.score;
                document.getElementById('ballsLeft').textContent = this.ballsLeft;

                if (this.ballsLeft === 0) {
                    alert('恭喜！游戏完成！总分：' + this.score);
                }
            }

            resetGame() {
                this.balls = [];
                this.score = 0;
                this.ballsLeft = 15;
                this.createBalls();
                this.updateUI();
            }

            toggleCamera() {
                // 简化版本，只是提示
                alert('2D版本暂不支持视角切换');
            }

            animate() {
                this.updatePhysics();
                this.checkPockets();
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }

        // 初始化游戏
        window.addEventListener('load', function() {
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'block';

            try {
                window.game = new SimplePoolGame();
            } catch (error) {
                console.error('游戏初始化失败:', error);
                document.getElementById('gameContainer').innerHTML =
                    '<div id="errorMessage">游戏初始化失败: ' + error.message +
                    '<br><br>这是一个简化的2D版本，如需完整3D体验，请确保网络连接正常并使用index.html</div>';
            }
        });
    </script>
</body>
</html>