<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D桌球游戏 - 离线版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            cursor: crosshair;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 100;
        }
        
        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .button:hover {
            background: rgba(255, 255, 255, 0.4);
        }
        
        #powerMeter {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid white;
            border-radius: 10px;
            z-index: 100;
        }
        
        #powerBar {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(to top, #00ff00, #ffff00, #ff0000);
            border-radius: 8px;
            transition: height 0.1s;
        }
        
        #loadingMessage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }
        
        #errorMessage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff6b6b;
            font-size: 20px;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 10px;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <div id="loadingMessage">
        <div>正在加载3D桌球游戏...</div>
        <div style="margin-top: 20px; font-size: 16px;">如果长时间无响应，请检查网络连接</div>
    </div>
    
    <div id="gameContainer" style="display: none;">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div>得分: <span id="score">0</span></div>
            <div>剩余球数: <span id="ballsLeft">15</span></div>
            <div>当前玩家: <span id="currentPlayer">玩家1</span></div>
        </div>
        
        
        <div id="controls">
            <div>使用鼠标瞄准，点击击球</div>
            <button class="button" onclick="game.resetGame()">重新开始</button>
            <button class="button" onclick="game.toggleCamera()">切换视角</button>
        </div>
        
        <div id="powerMeter">
            <div id="powerBar" style="height: 0%;"></div>
        </div>
    </div>
    
    <script>
        // 简化版3D桌球游戏（不依赖外部库）
        class SimplePoolGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
                
                this.balls = [];
                this.cueBall = null;
                this.isAiming = false;
                this.power = 0;
                this.maxPower = 100;
                this.powerIncreasing = true;
                
                this.score = 0;
                this.ballsLeft = 15;
                
                this.mouse = { x: 0, y: 0 };
                this.aimLine = { start: null, end: null };
                this.cueStick = { x: 0, y: 0, angle: 0, length: 120, visible: false };
                
                this.init();
            }
            
            init() {
                this.createBalls();
                this.setupControls();
                this.animate();
                this.updateUI();
            }
            
            createBalls() {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                const ballRadius = 15;
                
                // 白球（主球）
                this.cueBall = {
                    x: centerX - 200,
                    y: centerY,
                    vx: 0,
                    vy: 0,
                    radius: ballRadius,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);
                
                // 彩球（三角形排列）
                const colors = [
                    '#ff0000', '#0000ff', '#ffff00', '#800080', '#ffa500',
                    '#008000', '#800000', '#000000', '#ff69b4', '#00ffff',
                    '#ff1493', '#32cd32', '#8b4513', '#4169e1', '#ff6347'
                ];
                
                let ballIndex = 0;
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        if (ballIndex >= 15) break;
                        
                        const x = centerX + 100 + row * ballRadius * 1.8;
                        const y = centerY + (col - row / 2) * ballRadius * 2;
                        
                        this.balls.push({
                            x: x,
                            y: y,
                            vx: 0,
                            vy: 0,
                            radius: ballRadius,
                            color: colors[ballIndex],
                            type: 'target',
                            id: ballIndex + 1
                        });
                        ballIndex++;
                    }
                }
            }
            
            setupControls() {
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.mouse.x = e.clientX - rect.left;
                    this.mouse.y = e.clientY - rect.top;
                    
                    if (!this.isAiming) {
                        this.updateAimLine();
                    }
                });
                
                this.canvas.addEventListener('mousedown', (e) => {
                    if (e.button === 0) {
                        this.startAiming();
                    }
                });
                
                this.canvas.addEventListener('mouseup', (e) => {
                    if (e.button === 0 && this.isAiming) {
                        this.shoot();
                    }
                });
                
                window.addEventListener('resize', () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                });
            }
            
            updateAimLine() {
                if (!this.cueBall) return;
                
                const dx = this.mouse.x - this.cueBall.x;
                const dy = this.mouse.y - this.cueBall.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 0) {
                    const normalizedX = dx / distance;
                    const normalizedY = dy / distance;
                    
                    // 更新球杆位置
                    this.cueStick = {
                        angle: Math.atan2(dy, dx),
                        x: this.cueBall.x - normalizedX * 80,
                        y: this.cueBall.y - normalizedY * 80,
                        length: 120,
                        visible: true
                    };
                    
                    this.aimLine.start = {
                        x: this.cueBall.x - normalizedX * 30,
                        y: this.cueBall.y - normalizedY * 30
                    };
                    
                    this.aimLine.end = {
                        x: this.cueBall.x + normalizedX * 100,
                        y: this.cueBall.y + normalizedY * 100
                    };
                }
            }
            
            startAiming() {
                this.isAiming = true;
                this.power = 0;
                this.powerIncreasing = true;
                this.updatePowerMeter();
            }
            
            updatePowerMeter() {
                if (!this.isAiming) return;
                
                if (this.powerIncreasing) {
                    this.power += 2;
                    if (this.power >= this.maxPower) {
                        this.powerIncreasing = false;
                    }
                } else {
                    this.power -= 2;
                    if (this.power <= 0) {
                        this.powerIncreasing = true;
                    }
                }
                
                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = (this.power / this.maxPower * 100) + '%';
                
                requestAnimationFrame(() => this.updatePowerMeter());
            }
            
            shoot() {
                this.isAiming = false;
                
                const dx = this.mouse.x - this.cueBall.x;
                const dy = this.mouse.y - this.cueBall.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 0) {
                    const force = this.power * 0.8;  // 增加击球力度
                    this.cueBall.vx = (dx / distance) * force;
                    this.cueBall.vy = (dy / distance) * force;
                }
                
                // 隐藏球杆
                this.cueStick.visible = false;
                
                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = '0%';
            }
            
            updatePhysics() {
                // 更新球的位置
                this.balls.forEach(ball => {
                    ball.x += ball.vx;
                    ball.y += ball.vy;
                    
                    // 摩擦力（降低阻力，让球滚得更远）
                    ball.vx *= 0.995;
                    ball.vy *= 0.995;
                    
                    // 边界碰撞
                    if (ball.x - ball.radius < 50 || ball.x + ball.radius > this.canvas.width - 50) {
                        ball.vx = -ball.vx * 0.8;
                        ball.x = Math.max(50 + ball.radius, Math.min(this.canvas.width - 50 - ball.radius, ball.x));
                    }
                    if (ball.y - ball.radius < 50 || ball.y + ball.radius > this.canvas.height - 50) {
                        ball.vy = -ball.vy * 0.8;
                        ball.y = Math.max(50 + ball.radius, Math.min(this.canvas.height - 50 - ball.radius, ball.y));
                    }
                });
                
                // 球与球碰撞
                for (let i = 0; i < this.balls.length; i++) {
                    for (let j = i + 1; j < this.balls.length; j++) {
                        const ball1 = this.balls[i];
                        const ball2 = this.balls[j];
                        
                        const dx = ball2.x - ball1.x;
                        const dy = ball2.y - ball1.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < ball1.radius + ball2.radius) {
                            // 简单的弹性碰撞
                            const angle = Math.atan2(dy, dx);
                            const sin = Math.sin(angle);
                            const cos = Math.cos(angle);
                            
                            // 交换速度分量
                            const vx1 = ball1.vx * cos + ball1.vy * sin;
                            const vy1 = ball1.vy * cos - ball1.vx * sin;
                            const vx2 = ball2.vx * cos + ball2.vy * sin;
                            const vy2 = ball2.vy * cos - ball2.vx * sin;
                            
                            ball1.vx = vx2 * cos - vy1 * sin;
                            ball1.vy = vy1 * cos + vx2 * sin;
                            ball2.vx = vx1 * cos - vy2 * sin;
                            ball2.vy = vy2 * cos + vx1 * sin;
                            
                            // 分离球体
                            const overlap = ball1.radius + ball2.radius - distance;
                            const separateX = (dx / distance) * overlap * 0.5;
                            const separateY = (dy / distance) * overlap * 0.5;
                            
                            ball1.x -= separateX;
                            ball1.y -= separateY;
                            ball2.x += separateX;
                            ball2.y += separateY;
                        }
                    }
                }
            }
            
            render() {
                // 清空画布
                this.ctx.fillStyle = '#0d5016';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制桌边
                this.ctx.strokeStyle = '#8B4513';
                this.ctx.lineWidth = 10;
                this.ctx.strokeRect(45, 45, this.canvas.width - 90, this.canvas.height - 90);
                
                // 绘制球袋
                this.ctx.fillStyle = '#000000';
                const pockets = [
                    [50, 50], [this.canvas.width - 50, 50],
                    [50, this.canvas.height - 50], [this.canvas.width - 50, this.canvas.height - 50],
                    [this.canvas.width / 2, 50], [this.canvas.width / 2, this.canvas.height - 50]
                ];
                
                pockets.forEach(pocket => {
                    this.ctx.beginPath();
                    this.ctx.arc(pocket[0], pocket[1], 25, 0, Math.PI * 2);
                    this.ctx.fill();
                });
                
                // 绘制球杆
                if (this.cueStick.visible && !this.isAiming) {
                    this.drawCueStick();
                }
                
                // 绘制瞄准线
                if (this.aimLine.start && this.aimLine.end && !this.isAiming) {
                    this.ctx.strokeStyle = '#ffffff';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.aimLine.start.x, this.aimLine.start.y);
                    this.ctx.lineTo(this.aimLine.end.x, this.aimLine.end.y);
                    this.ctx.stroke();
                    this.ctx.setLineDash([]);
                }
                
                // 绘制球
                this.balls.forEach(ball => {
                    this.ctx.fillStyle = ball.color;
                    this.ctx.beginPath();
                    this.ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
                    this.ctx.fill();
                    
                    // 球的边框
                    this.ctx.strokeStyle = '#333333';
                    this.ctx.lineWidth = 1;
                    this.ctx.stroke();
                    
                    // 球号
                    if (ball.type === 'target') {
                        this.ctx.fillStyle = '#ffffff';
                        this.ctx.font = '12px Arial';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillText(ball.id.toString(), ball.x, ball.y + 4);
                    }
                });
            }
            
            drawCueStick() {
                const ctx = this.ctx;
                const stick = this.cueStick;
                
                ctx.save();
                ctx.translate(stick.x, stick.y);
                ctx.rotate(stick.angle);
                
                // 杆身主体（木质）
                const gradient1 = ctx.createLinearGradient(0, -4, 0, 4);
                gradient1.addColorStop(0, '#D2691E');
                gradient1.addColorStop(0.5, '#CD853F');
                gradient1.addColorStop(1, '#A0522D');
                
                ctx.fillStyle = gradient1;
                ctx.fillRect(0, -4, stick.length * 0.8, 8);
                
                // 杆头（金属）
                ctx.fillStyle = '#2F4F4F';
                ctx.fillRect(stick.length * 0.8, -3, stick.length * 0.1, 6);
                
                // 握把（皮质）
                const gradient2 = ctx.createLinearGradient(0, -5, 0, 5);
                gradient2.addColorStop(0, '#654321');
                gradient2.addColorStop(0.5, '#8B4513');
                gradient2.addColorStop(1, '#654321');
                
                ctx.fillStyle = gradient2;
                ctx.fillRect(-stick.length * 0.3, -5, stick.length * 0.25, 10);
                
                // 握把装饰环
                ctx.fillStyle = '#8B4513';
                for (let i = 0; i < 3; i++) {
                    const x = -stick.length * 0.2 + i * 8;
                    ctx.fillRect(x, -5, 2, 10);
                }
                
                // 杆尾配重
                ctx.fillStyle = '#8B0000';
                ctx.fillRect(-stick.length * 0.35, -4, stick.length * 0.05, 8);
                
                // 木纹效果
                ctx.strokeStyle = '#A0522D';
                ctx.lineWidth = 0.5;
                for (let i = 0; i < 5; i++) {
                    const x = i * (stick.length * 0.8 / 5);
                    ctx.beginPath();
                    ctx.moveTo(x, -3);
                    ctx.lineTo(x, 3);
                    ctx.stroke();
                }
                
                // 球杆边框
                ctx.strokeStyle = '#654321';
                ctx.lineWidth = 1;
                ctx.strokeRect(-stick.length * 0.35, -5, stick.length * 1.25, 10);
                
                ctx.restore();
            }
            
            checkPockets() {
                const pockets = [
                    [50, 50], [this.canvas.width - 50, 50],
                    [50, this.canvas.height - 50], [this.canvas.width - 50, this.canvas.height - 50],
                    [this.canvas.width / 2, 50], [this.canvas.width / 2, this.canvas.height - 50]
                ];
                
                this.balls = this.balls.filter(ball => {
                    for (let pocket of pockets) {
                        const dx = ball.x - pocket[0];
                        const dy = ball.y - pocket[1];
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < 25) {
                            if (ball.type === 'target') {
                                this.score += 10;
                                this.ballsLeft--;
                                this.updateUI();
                            } else if (ball.type === 'cue') {
                                // 重新放置白球
                                setTimeout(() => this.resetCueBall(), 1000);
                            }
                            return false; // 移除球
                        }
                    }
                    return true; // 保留球
                });
            }
            
            resetCueBall() {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                
                this.cueBall = {
                    x: centerX - 200,
                    y: centerY,
                    vx: 0,
                    vy: 0,
                    radius: 15,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);
            }
            
            updateUI() {
                document.getElementById('score').textContent = this.score;
                document.getElementById('ballsLeft').textContent = this.ballsLeft;
                
                if (this.ballsLeft === 0) {
                    alert('恭喜！游戏完成！总分：' + this.score);
                }
            }
            
            resetGame() {
                this.balls = [];
                this.score = 0;
                this.ballsLeft = 15;
                this.createBalls();
                this.updateUI();
            }
            
            toggleCamera() {
                // 简化版本，只是提示
                alert('2D版本暂不支持视角切换');
            }
            
            animate() {
                this.updatePhysics();
                this.checkPockets();
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // 初始化游戏
        window.addEventListener('load', function() {
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'block';
            
            try {
                window.game = new SimplePoolGame();
            } catch (error) {
                console.error('游戏初始化失败:', error);
                document.getElementById('gameContainer').innerHTML = 
                    '<div id="errorMessage">游戏初始化失败: ' + error.message + 
                    '<br><br>这是一个简化的2D版本，如需完整3D体验，请确保网络连接正常并使用index.html</div>';
            }
        });
    </script>
</body>
</html>