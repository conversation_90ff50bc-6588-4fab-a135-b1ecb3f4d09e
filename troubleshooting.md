# 3D桌球游戏故障排除指南

如果您遇到"根本不能玩"的问题，请按照以下步骤进行排查：

## 🔧 常见问题及解决方案

### 1. 网络连接问题
**症状**：页面显示"Three.js 库加载失败"或"Cannon.js 库加载失败"

**解决方案**：
- 检查网络连接是否正常
- 尝试刷新页面（Ctrl + F5 强制刷新）
- 使用离线版本：打开 `index_offline.html`

### 2. 浏览器兼容性问题
**症状**：页面空白或显示错误信息

**解决方案**：
- 使用现代浏览器（Chrome 60+, Firefox 55+, Safari 12+, Edge 79+）
- 确保浏览器支持WebGL
- 更新浏览器到最新版本

### 3. WebGL支持问题
**症状**：游戏无法启动或显示黑屏

**检查WebGL支持**：
1. 访问 https://get.webgl.org/
2. 如果看到旋转的立方体，说明WebGL正常
3. 如果不支持，请：
   - 更新显卡驱动
   - 在浏览器设置中启用硬件加速
   - 使用离线版本（2D版本）

### 4. JavaScript错误
**症状**：控制台显示错误信息

**解决方案**：
1. 按F12打开开发者工具
2. 查看Console标签页的错误信息
3. 常见错误及解决方法：
   - `THREE is not defined`：网络问题，使用离线版本
   - `CANNON is not defined`：物理引擎加载失败，使用离线版本
   - `WebGL context lost`：显卡问题，重启浏览器

## 📁 文件说明

### 主要文件
- `index.html` - 完整3D版本（需要网络连接）
- `index_offline.html` - 简化2D版本（无需网络）
- `js/game.js` - 3D游戏核心逻辑

### 推荐使用顺序
1. 首先尝试 `index.html`（完整3D体验）
2. 如果有问题，使用 `index_offline.html`（2D版本）
3. 如果仍有问题，查看下面的详细排查步骤

## 🛠️ 详细排查步骤

### 步骤1：检查文件完整性
确保以下文件存在：
```
3d/
├── index.html
├── index_offline.html
├── js/
│   └── game.js
├── README.md
└── troubleshooting.md
```

### 步骤2：测试网络连接
1. 打开 `index.html`
2. 如果看到错误信息，说明CDN无法访问
3. 改用 `index_offline.html`

### 步骤3：检查浏览器控制台
1. 按F12打开开发者工具
2. 点击Console标签
3. 刷新页面，查看是否有红色错误信息
4. 根据错误信息进行相应处理

### 步骤4：测试基本功能
在 `index_offline.html` 中测试：
1. 页面是否正常加载
2. 是否能看到桌球台和球
3. 鼠标移动是否显示瞄准线
4. 点击是否能击球

## 🎮 操作说明

### 3D版本 (index.html)
- 鼠标移动：瞄准
- 鼠标左键按住：蓄力（观察右侧力量条）
- 鼠标左键松开：击球
- 重新开始按钮：重置游戏
- 切换视角按钮：改变相机角度

### 2D版本 (index_offline.html)
- 鼠标移动：瞄准（显示虚线）
- 鼠标左键按住：蓄力（观察右侧力量条）
- 鼠标左键松开：击球
- 重新开始按钮：重置游戏

## 🔍 性能优化建议

### 如果游戏运行缓慢
1. 关闭其他浏览器标签页
2. 关闭不必要的程序
3. 降低浏览器缩放比例
4. 使用2D版本（性能要求更低）

### 如果画面卡顿
1. 更新显卡驱动
2. 在浏览器设置中启用硬件加速
3. 尝试不同的浏览器

## 📞 获取帮助

如果以上方法都无法解决问题，请：

1. **记录错误信息**：
   - 截图错误页面
   - 复制控制台错误信息
   - 记录使用的浏览器和版本

2. **提供系统信息**：
   - 操作系统版本
   - 浏览器版本
   - 显卡型号

3. **尝试的解决方案**：
   - 列出已经尝试过的方法
   - 说明具体的问题现象

## ✅ 成功运行的标志

### 3D版本正常运行时应该看到：
- 绿色的3D桌球台
- 立体的球体（白球 + 15个彩球）
- 可以用鼠标控制的球杆
- 右侧的力量条
- 左上角的得分显示

### 2D版本正常运行时应该看到：
- 绿色的平面桌球台
- 圆形的球（带编号）
- 鼠标移动时的瞄准虚线
- 右侧的力量条
- 左上角的得分显示

---

**记住**：如果3D版本有问题，2D版本（`index_offline.html`）是一个很好的备选方案！