<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D桌球游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            cursor: crosshair;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 100;
        }

        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        #powerMeter {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid white;
            border-radius: 10px;
            z-index: 100;
        }

        #powerBar {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(to top, #00ff00, #ffff00, #ff0000);
            border-radius: 8px;
            transition: height 0.1s;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>

        <div id="ui">
            <div>得分: <span id="score">0</span></div>
            <div>剩余球数: <span id="ballsLeft">15</span></div>
            <div>当前玩家: <span id="currentPlayer">玩家1</span></div>
        </div>

        <div id="controls">
            <div>使用鼠标瞄准，点击击球</div>
            <button class="button" onclick="game.resetGame()">重新开始</button>
            <button class="button" onclick="game.toggleCamera()">切换视角</button>
        </div>

        <div id="powerMeter">
            <div id="powerBar" style="height: 0%;"></div>
        </div>
    </div>

    <script>
        // 简化版3D桌球游戏（使用2D Canvas模拟3D效果）
        class Enhanced3DPoolGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;

                // 游戏状态
                this.balls = [];
                this.cueBall = null;
                this.isAiming = false;
                this.power = 0;
                this.maxPower = 100;
                this.powerIncreasing = true;

                this.score = 0;
                this.ballsLeft = 15;
                this.currentPlayer = 1;

                // 控制变量
                this.mouse = { x: 0, y: 0 };
                this.camera = {
                    x: 0, y: -200, z: 300,
                    angleX: 0.3, angleY: 0, angleZ: 0
                };

                // 3D投影参数
                this.perspective = 600;
                this.centerX = this.canvas.width / 2;
                this.centerY = this.canvas.height / 2;

                this.init();
            }

            init() {
                this.createBalls();
                this.setupControls();
                this.animate();
                this.updateUI();
            }

            // 3D到2D投影函数
            project3D(x, y, z) {
                // 应用相机变换
                const dx = x - this.camera.x;
                const dy = y - this.camera.y;
                const dz = z - this.camera.z;

                // 简单的透视投影
                const scale = this.perspective / (this.perspective + dz);
                return {
                    x: this.centerX + dx * scale,
                    y: this.centerY + dy * scale,
                    scale: scale
                };
            }

            createBalls() {
                const ballRadius = 12;

                // 白球（主球）
                this.cueBall = {
                    x: -150, y: 0, z: 0,
                    vx: 0, vy: 0, vz: 0,
                    radius: ballRadius,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);

                // 彩球（三角形排列）
                const colors = [
                    '#ff0000', '#0000ff', '#ffff00', '#800080', '#ffa500',
                    '#008000', '#800000', '#000000', '#ff69b4', '#00ffff',
                    '#ff1493', '#32cd32', '#8b4513', '#4169e1', '#ff6347'
                ];

                let ballIndex = 0;
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        if (ballIndex >= 15) break;

                        const x = 100 + row * ballRadius * 1.8;
                        const z = (col - row / 2) * ballRadius * 2;

                        this.balls.push({
                            x: x, y: 0, z: z,
                            vx: 0, vy: 0, vz: 0,
                            radius: ballRadius,
                            color: colors[ballIndex],
                            type: 'target',
                            id: ballIndex + 1
                        });
                        ballIndex++;
                    }
                }
            }

            setupControls() {
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.mouse.x = e.clientX - rect.left;
                    this.mouse.y = e.clientY - rect.top;
                });

                this.canvas.addEventListener('mousedown', (e) => {
                    if (e.button === 0) {
                        this.startAiming();
                    }
                });

                this.canvas.addEventListener('mouseup', (e) => {
                    if (e.button === 0 && this.isAiming) {
                        this.shoot();
                    }
                });

                window.addEventListener('resize', () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                    this.centerX = this.canvas.width / 2;
                    this.centerY = this.canvas.height / 2;
                });
            }

            startAiming() {
                this.isAiming = true;
                this.power = 0;
                this.powerIncreasing = true;
                this.updatePowerMeter();
            }

            updatePowerMeter() {
                if (!this.isAiming) return;

                if (this.powerIncreasing) {
                    this.power += 2;
                    if (this.power >= this.maxPower) {
                        this.powerIncreasing = false;
                    }
                } else {
                    this.power -= 2;
                    if (this.power <= 0) {
                        this.powerIncreasing = true;
                    }
                }

                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = (this.power / this.maxPower * 100) + '%';

                requestAnimationFrame(() => this.updatePowerMeter());
            }

            shoot() {
                this.isAiming = false;

                if (!this.cueBall) return;

                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length > 0) {
                    const force = this.power * 0.5;
                    this.cueBall.vx = (dx / length) * force;
                    this.cueBall.vz = (dy / length) * force * 0.5; // Z轴移动较小
                }

                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = '0%';
            }

            updatePhysics() {
                // 更新球的位置
                this.balls.forEach(ball => {
                    ball.x += ball.vx;
                    ball.y += ball.vy;
                    ball.z += ball.vz;

                    // 摩擦力
                    ball.vx *= 0.98;
                    ball.vy *= 0.98;
                    ball.vz *= 0.98;

                    // 边界碰撞
                    if (Math.abs(ball.x) > 300) {
                        ball.vx = -ball.vx * 0.8;
                        ball.x = Math.sign(ball.x) * 300;
                    }
                    if (Math.abs(ball.z) > 150) {
                        ball.vz = -ball.vz * 0.8;
                        ball.z = Math.sign(ball.z) * 150;
                    }

                    // 停止条件
                    if (Math.abs(ball.vx) < 0.5 && Math.abs(ball.vz) < 0.5) {
                        ball.vx = 0;
                        ball.vz = 0;
                    }
                });

                // 球与球碰撞
                for (let i = 0; i < this.balls.length; i++) {
                    for (let j = i + 1; j < this.balls.length; j++) {
                        const ball1 = this.balls[i];
                        const ball2 = this.balls[j];

                        const dx = ball1.x - ball2.x;
                        const dz = ball1.z - ball2.z;
                        const distance = Math.sqrt(dx * dx + dz * dz);

                        if (distance < ball1.radius * 2) {
                            // 简单的碰撞响应
                            const angle = Math.atan2(dz, dx);
                            const sin = Math.sin(angle);
                            const cos = Math.cos(angle);

                            // 分离球
                            const overlap = ball1.radius * 2 - distance;
                            ball1.x += cos * overlap * 0.5;
                            ball1.z += sin * overlap * 0.5;
                            ball2.x -= cos * overlap * 0.5;
                            ball2.z -= sin * overlap * 0.5;

                            // 交换速度分量
                            const v1x = ball1.vx * cos + ball1.vz * sin;
                            const v1z = ball1.vz * cos - ball1.vx * sin;
                            const v2x = ball2.vx * cos + ball2.vz * sin;
                            const v2z = ball2.vz * cos - ball2.vx * sin;

                            ball1.vx = v2x * cos - v1z * sin;
                            ball1.vz = v1z * cos + v2x * sin;
                            ball2.vx = v1x * cos - v2z * sin;
                            ball2.vz = v2z * cos + v1x * sin;
                        }
                    }
                }
            }

            checkPockets() {
                const pockets = [
                    {x: -300, z: -150}, {x: 300, z: -150},
                    {x: -300, z: 150}, {x: 300, z: 150},
                    {x: 0, z: -150}, {x: 0, z: 150}
                ];

                this.balls = this.balls.filter(ball => {
                    for (let pocket of pockets) {
                        const dx = ball.x - pocket.x;
                        const dz = ball.z - pocket.z;
                        const distance = Math.sqrt(dx * dx + dz * dz);

                        if (distance < 30) {
                            if (ball.type === 'target') {
                                this.score += 10;
                                this.ballsLeft--;
                                this.updateUI();
                            } else if (ball.type === 'cue') {
                                setTimeout(() => this.resetCueBall(), 1000);
                            }
                            return false;
                        }
                    }
                    return true;
                });
            }

            resetCueBall() {
                this.cueBall = {
                    x: -150, y: 0, z: 0,
                    vx: 0, vy: 0, vz: 0,
                    radius: 12,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);
            }

            updateUI() {
                document.getElementById('score').textContent = this.score;
                document.getElementById('ballsLeft').textContent = this.ballsLeft;
                document.getElementById('currentPlayer').textContent = `玩家${this.currentPlayer}`;

                if (this.ballsLeft === 0) {
                    alert('恭喜！游戏完成！总分：' + this.score);
                }
            }

            resetGame() {
                this.balls = [];
                this.score = 0;
                this.ballsLeft = 15;
                this.currentPlayer = 1;
                this.createBalls();
                this.updateUI();
            }

            toggleCamera() {
                // 切换相机角度
                if (this.camera.angleX > 0.2) {
                    this.camera.angleX = 0.1;
                    this.camera.y = -100;
                    this.camera.z = 200;
                } else {
                    this.camera.angleX = 0.3;
                    this.camera.y = -200;
                    this.camera.z = 300;
                }
            }

            render() {
                // 清空画布
                this.ctx.fillStyle = '#1a1a2e';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // 绘制桌面
                this.drawTable();

                // 绘制球（按Z轴排序以正确显示深度）
                const sortedBalls = [...this.balls].sort((a, b) => b.z - a.z);
                sortedBalls.forEach(ball => this.drawBall(ball));

                // 绘制瞄准线
                if (!this.isAiming && this.cueBall) {
                    this.drawAimingLine();
                }
            }

            drawTable() {
                // 桌面（3D效果）
                const corners = [
                    this.project3D(-300, 0, -150),
                    this.project3D(300, 0, -150),
                    this.project3D(300, 0, 150),
                    this.project3D(-300, 0, 150)
                ];

                this.ctx.fillStyle = '#0d5016';
                this.ctx.beginPath();
                this.ctx.moveTo(corners[0].x, corners[0].y);
                corners.forEach(corner => this.ctx.lineTo(corner.x, corner.y));
                this.ctx.closePath();
                this.ctx.fill();

                // 桌边
                this.ctx.strokeStyle = '#8B4513';
                this.ctx.lineWidth = 8;
                this.ctx.stroke();

                // 球袋
                const pockets = [
                    {x: -300, z: -150}, {x: 300, z: -150},
                    {x: -300, z: 150}, {x: 300, z: 150},
                    {x: 0, z: -150}, {x: 0, z: 150}
                ];

                this.ctx.fillStyle = '#000';
                pockets.forEach(pocket => {
                    const proj = this.project3D(pocket.x, 0, pocket.z);
                    this.ctx.beginPath();
                    this.ctx.arc(proj.x, proj.y, 20 * proj.scale, 0, Math.PI * 2);
                    this.ctx.fill();
                });
            }

            drawBall(ball) {
                const proj = this.project3D(ball.x, ball.y, ball.z);
                const radius = ball.radius * proj.scale;

                // 球的阴影
                this.ctx.fillStyle = 'rgba(0,0,0,0.3)';
                this.ctx.beginPath();
                this.ctx.arc(proj.x + 2, proj.y + 2, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 球体渐变
                const gradient = this.ctx.createRadialGradient(
                    proj.x - radius/3, proj.y - radius/3, 0,
                    proj.x, proj.y, radius
                );

                if (ball.type === 'cue') {
                    gradient.addColorStop(0, '#ffffff');
                    gradient.addColorStop(1, '#e0e0e0');
                } else {
                    gradient.addColorStop(0, ball.color);
                    gradient.addColorStop(0.7, ball.color);
                    gradient.addColorStop(1, '#333');
                }

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(proj.x, proj.y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 球的边框
                this.ctx.strokeStyle = '#333';
                this.ctx.lineWidth = 1;
                this.ctx.stroke();

                // 球号
                if (ball.type === 'target') {
                    this.ctx.fillStyle = '#fff';
                    this.ctx.font = `${Math.max(8, radius * 0.6)}px Arial`;
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText(ball.id.toString(), proj.x, proj.y + radius * 0.2);
                }
            }

            drawAimingLine() {
                if (!this.cueBall) return;

                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length > 0) {
                    const aimLength = Math.min(length, 150);
                    const endX = cueBallProj.x + (dx / length) * aimLength;
                    const endY = cueBallProj.y + (dy / length) * aimLength;

                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(cueBallProj.x, cueBallProj.y);
                    this.ctx.lineTo(endX, endY);
                    this.ctx.stroke();
                    this.ctx.setLineDash([]);
                }
            }

            animate() {
                this.updatePhysics();
                this.checkPockets();
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }

        // 游戏初始化
        window.addEventListener('load', function() {
            try {
                window.game = new Enhanced3DPoolGame();
                console.log('3D桌球游戏加载成功！');
            } catch (error) {
                console.error('游戏初始化失败:', error);
                document.body.innerHTML = `
                    <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                color: white; text-align: center; font-size: 20px; background: rgba(0,0,0,0.8);
                                padding: 30px; border-radius: 10px;">
                        <h2>游戏加载失败</h2>
                        <p>错误信息: ${error.message}</p>
                        <p><a href="index_offline.html" style="color: #4CAF50;">点击这里使用离线版本</a></p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>