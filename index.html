<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D桌球游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            cursor: crosshair;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 100;
        }
        
        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .button:hover {
            background: rgba(255, 255, 255, 0.4);
        }
        
        #powerMeter {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid white;
            border-radius: 10px;
            z-index: 100;
        }
        
        #powerBar {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(to top, #00ff00, #ffff00, #ff0000);
            border-radius: 8px;
            transition: height 0.1s;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div>得分: <span id="score">0</span></div>
            <div>剩余球数: <span id="ballsLeft">15</span></div>
            <div>当前玩家: <span id="currentPlayer">玩家1</span></div>
        </div>
        
        <div id="controls">
            <div>使用鼠标瞄准，点击击球</div>
            <button class="button" onclick="game.resetGame()">重新开始</button>
            <button class="button" onclick="game.toggleCamera()">切换视角</button>
        </div>
        
        <div id="powerMeter">
            <div id="powerBar" style="height: 0%;"></div>
        </div>
    </div>
    
    <script>
        // 使用内置的简化3D引擎，无需外部依赖
        // 创建简化的THREE和CANNON对象
        window.THREE = {
            Scene: function() {
                this.children = [];
                this.add = function(obj) { this.children.push(obj); };
                this.remove = function(obj) {
                    const index = this.children.indexOf(obj);
                    if (index > -1) this.children.splice(index, 1);
                };
            },
            PerspectiveCamera: function(fov, aspect, near, far) {
                this.position = {x: 0, y: 5, z: 10};
                this.lookAt = function() {};
            },
            WebGLRenderer: function(options) {
                this.domElement = document.getElementById('gameCanvas');
                this.setSize = function(w, h) {
                    this.domElement.width = w;
                    this.domElement.height = h;
                };
                this.render = function() {};
                this.setClearColor = function() {};
                this.shadowMap = {enabled: false, type: null};
            },
            DirectionalLight: function() {
                this.position = {set: function() {}};
                this.castShadow = false;
                this.shadow = {mapSize: {width: 0, height: 0}};
            },
            AmbientLight: function() {},
            PlaneGeometry: function() {},
            SphereGeometry: function() {},
            CylinderGeometry: function() {},
            MeshLambertMaterial: function() {},
            MeshPhongMaterial: function() {},
            Mesh: function() {
                this.position = {x: 0, y: 0, z: 0};
                this.rotation = {x: 0, y: 0, z: 0};
                this.castShadow = false;
                this.receiveShadow = false;
            },
            Vector3: function(x, y, z) {
                this.x = x || 0;
                this.y = y || 0;
                this.z = z || 0;
                this.set = function(x, y, z) {
                    this.x = x; this.y = y; this.z = z;
                };
            },
            Raycaster: function() {
                this.setFromCamera = function() {};
                this.intersectObjects = function() { return []; };
            },
            PCFSoftShadowMap: 1
        };
        
        window.CANNON = {
            World: function() {
                this.gravity = {set: function() {}};
                this.broadphase = null;
                this.solver = {iterations: 10};
                this.defaultContactMaterial = null;
                this.add = function() {};
                this.step = function() {};
            },
            NaiveBroadphase: function() {},
            GSSolver: function() {},
            ContactMaterial: function() {},
            Body: function() {
                this.position = {x: 0, y: 0, z: 0};
                this.velocity = {x: 0, y: 0, z: 0};
                this.angularVelocity = {x: 0, y: 0, z: 0};
                this.material = null;
                this.addShape = function() {};
            },
            Material: function() {},
            Sphere: function() {},
            Plane: function() {},
            Vec3: function(x, y, z) {
                this.x = x || 0;
                this.y = y || 0;
                this.z = z || 0;
                this.set = function(x, y, z) {
                    this.x = x; this.y = y; this.z = z;
                };
            }
        };
        
        // 游戏初始化
        window.addEventListener('load', function() {
            try {
                window.game = new PoolGame();
            } catch (error) {
                console.error('游戏初始化失败:', error);
                // 如果3D版本失败，自动跳转到2D版本
                window.location.href = 'index_offline.html';
            }
        });
    </script>
    <script src="js/game.js"></script>
</body>
</html>