<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D桌球游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            cursor: crosshair;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 100;
        }

        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        #powerMeter {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid white;
            border-radius: 10px;
            z-index: 100;
        }

        #powerBar {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(to top, #00ff00, #ffff00, #ff0000);
            border-radius: 8px;
            transition: height 0.1s;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>

        <div id="ui">
            <div>得分: <span id="score">0</span></div>
            <div>剩余球数: <span id="ballsLeft">15</span></div>
            <div>当前玩家: <span id="currentPlayer">玩家1</span></div>
        </div>

        <div id="controls">
            <div>使用鼠标瞄准，点击击球</div>
            <div style="font-size: 14px; margin-top: 5px;">
                按 H 键切换瞄准辅助 | 按 P 键切换精确模式
            </div>
            <button class="button" onclick="game.resetGame()">重新开始</button>
            <button class="button" onclick="game.toggleCamera()">切换视角</button>
        </div>

        <div id="powerMeter">
            <div id="powerBar" style="height: 0%;"></div>
        </div>
    </div>

    <script>
        // 简化版3D桌球游戏（使用2D Canvas模拟3D效果）
        class Enhanced3DPoolGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;

                // 游戏状态
                this.balls = [];
                this.cueBall = null;
                this.isAiming = false;
                this.power = 0;
                this.maxPower = 100;
                this.powerIncreasing = true;

                this.score = 0;
                this.ballsLeft = 15;
                this.currentPlayer = 1;

                // 控制变量
                this.mouse = { x: 0, y: 0 };
                this.camera = {
                    x: 0, y: -200, z: 300,
                    angleX: 0.3, angleY: 0, angleZ: 0
                };

                // 3D投影参数
                this.perspective = 600;
                this.centerX = this.canvas.width / 2;
                this.centerY = this.canvas.height / 2;
                this.ballRadius = 12;

                // 球杆相关
                this.cueStick = {
                    visible: false,
                    x: 0, y: 0, z: 0,
                    angle: 0,
                    distance: 80,  // 球杆距离白球的距离
                    length: 150,   // 球杆长度
                    aimPower: 0    // 瞄准时的后拉距离
                };

                // 瞄准系统
                this.aimingSystem = {
                    targetBall: null,        // 瞄准的目标球
                    hitPoint: null,          // 撞击点
                    pocketTarget: null,      // 目标球袋
                    aimLine: [],            // 瞄准线段
                    ghostBall: null,        // 虚拟球位置
                    showHelper: true,       // 显示瞄准辅助
                    precision: false        // 精确瞄准模式
                };

                // 球袋位置
                this.pockets = [
                    {x: -300, z: -150, id: 'top-left'},
                    {x: 300, z: -150, id: 'top-right'},
                    {x: -300, z: 150, id: 'bottom-left'},
                    {x: 300, z: 150, id: 'bottom-right'},
                    {x: 0, z: -150, id: 'top-center'},
                    {x: 0, z: 150, id: 'bottom-center'}
                ];

                this.init();
            }

            init() {
                this.createBalls();
                this.setupControls();
                this.animate();
                this.updateUI();
            }

            // 3D到2D投影函数
            project3D(x, y, z) {
                // 应用相机变换
                const dx = x - this.camera.x;
                const dy = y - this.camera.y;
                const dz = z - this.camera.z;

                // 简单的透视投影
                const scale = this.perspective / (this.perspective + dz);
                return {
                    x: this.centerX + dx * scale,
                    y: this.centerY + dy * scale,
                    scale: scale
                };
            }

            createBalls() {
                const ballRadius = 12;

                // 白球（主球）
                this.cueBall = {
                    x: -150, y: 0, z: 0,
                    vx: 0, vy: 0, vz: 0,
                    radius: ballRadius,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);

                // 彩球（三角形排列）
                const colors = [
                    '#ff0000', '#0000ff', '#ffff00', '#800080', '#ffa500',
                    '#008000', '#800000', '#000000', '#ff69b4', '#00ffff',
                    '#ff1493', '#32cd32', '#8b4513', '#4169e1', '#ff6347'
                ];

                let ballIndex = 0;
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        if (ballIndex >= 15) break;

                        const x = 100 + row * ballRadius * 1.8;
                        const z = (col - row / 2) * ballRadius * 2;

                        this.balls.push({
                            x: x, y: 0, z: z,
                            vx: 0, vy: 0, vz: 0,
                            radius: ballRadius,
                            color: colors[ballIndex],
                            type: 'target',
                            id: ballIndex + 1
                        });
                        ballIndex++;
                    }
                }
            }

            setupControls() {
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.mouse.x = e.clientX - rect.left;
                    this.mouse.y = e.clientY - rect.top;

                    // 更新球杆位置和角度
                    this.updateCueStick();
                });

                this.canvas.addEventListener('mousedown', (e) => {
                    if (e.button === 0) {
                        this.startAiming();
                    }
                });

                this.canvas.addEventListener('mouseup', (e) => {
                    if (e.button === 0 && this.isAiming) {
                        this.shoot();
                    }
                });

                window.addEventListener('resize', () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                    this.centerX = this.canvas.width / 2;
                    this.centerY = this.canvas.height / 2;
                });

                // 键盘控制
                window.addEventListener('keydown', (e) => {
                    switch(e.key.toLowerCase()) {
                        case 'h':
                            // 切换瞄准辅助
                            this.aimingSystem.showHelper = !this.aimingSystem.showHelper;
                            break;
                        case 'p':
                            // 切换精确瞄准模式
                            this.aimingSystem.precision = !this.aimingSystem.precision;
                            break;
                    }
                });
            }

            updateCueStick() {
                if (!this.cueBall) return;

                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length > 0) {
                    // 计算球杆角度
                    this.cueStick.angle = Math.atan2(dy, dx);

                    // 计算球杆在3D空间中的位置
                    const normalizedX = dx / length;
                    const normalizedY = dy / length;

                    // 根据瞄准力度调整球杆距离
                    const totalDistance = this.cueStick.distance + this.cueStick.aimPower;

                    // 球杆位置（在白球后方）
                    this.cueStick.x = this.cueBall.x - normalizedX * totalDistance * 0.5;
                    this.cueStick.y = this.cueBall.y;
                    this.cueStick.z = this.cueBall.z - normalizedY * totalDistance * 0.3;

                    // 更新瞄准系统
                    this.updateAimingSystem(normalizedX, normalizedY);

                    this.cueStick.visible = true;
                } else {
                    this.cueStick.visible = false;
                }
            }

            updateAimingSystem(dirX, dirY) {
                // 重置瞄准系统
                this.aimingSystem.targetBall = null;
                this.aimingSystem.hitPoint = null;
                this.aimingSystem.pocketTarget = null;
                this.aimingSystem.ghostBall = null;

                // 从白球发射射线，寻找第一个碰撞的球
                const rayStart = {x: this.cueBall.x, z: this.cueBall.z};
                const rayDir = {x: dirX, z: dirY * 0.5}; // Z轴移动较小

                let closestBall = null;
                let closestDistance = Infinity;

                // 检测与其他球的碰撞
                this.balls.forEach(ball => {
                    if (ball === this.cueBall) return;

                    // 计算射线与球的交点
                    const toBall = {
                        x: ball.x - rayStart.x,
                        z: ball.z - rayStart.z
                    };

                    // 投影到射线方向
                    const projection = toBall.x * rayDir.x + toBall.z * rayDir.z;
                    if (projection <= 0) return; // 球在射线后方

                    // 计算最近点
                    const closestPoint = {
                        x: rayStart.x + rayDir.x * projection,
                        z: rayStart.z + rayDir.z * projection
                    };

                    // 计算距离
                    const distance = Math.sqrt(
                        Math.pow(closestPoint.x - ball.x, 2) +
                        Math.pow(closestPoint.z - ball.z, 2)
                    );

                    // 检查是否碰撞（考虑球的半径）
                    if (distance <= ball.radius * 2 && projection < closestDistance) {
                        closestDistance = projection;
                        closestBall = ball;

                        // 计算撞击点
                        this.aimingSystem.hitPoint = {
                            x: ball.x - rayDir.x * ball.radius,
                            z: ball.z - rayDir.z * ball.radius
                        };
                    }
                });

                if (closestBall) {
                    this.aimingSystem.targetBall = closestBall;

                    // 计算虚拟球位置（白球撞击目标球时的位置）
                    const dirToBall = {
                        x: closestBall.x - this.cueBall.x,
                        z: closestBall.z - this.cueBall.z
                    };
                    const distToBall = Math.sqrt(dirToBall.x * dirToBall.x + dirToBall.z * dirToBall.z);

                    if (distToBall > 0) {
                        const normalizedDir = {
                            x: dirToBall.x / distToBall,
                            z: dirToBall.z / distToBall
                        };

                        this.aimingSystem.ghostBall = {
                            x: closestBall.x - normalizedDir.x * (this.cueBall.radius * 2),
                            z: closestBall.z - normalizedDir.z * (this.cueBall.radius * 2)
                        };

                        // 寻找最佳球袋
                        this.findBestPocket(closestBall);
                    }
                }
            }

            findBestPocket(targetBall) {
                let bestPocket = null;
                let bestScore = -1;

                this.pockets.forEach(pocket => {
                    // 计算目标球到球袋的方向
                    const toPocket = {
                        x: pocket.x - targetBall.x,
                        z: pocket.z - targetBall.z
                    };
                    const distance = Math.sqrt(toPocket.x * toPocket.x + toPocket.z * toPocket.z);

                    if (distance > 0) {
                        // 计算角度评分（越直越好）
                        const normalizedToPocket = {
                            x: toPocket.x / distance,
                            z: toPocket.z / distance
                        };

                        // 计算白球撞击目标球后，目标球的运动方向
                        const ballDir = {
                            x: targetBall.x - this.cueBall.x,
                            z: targetBall.z - this.cueBall.z
                        };
                        const ballDirLength = Math.sqrt(ballDir.x * ballDir.x + ballDir.z * ballDir.z);

                        if (ballDirLength > 0) {
                            const normalizedBallDir = {
                                x: ballDir.x / ballDirLength,
                                z: ballDir.z / ballDirLength
                            };

                            // 计算角度匹配度
                            const dotProduct = normalizedBallDir.x * normalizedToPocket.x +
                                             normalizedBallDir.z * normalizedToPocket.z;

                            // 距离因子（越近越好）
                            const distanceFactor = 1 / (1 + distance * 0.01);

                            const score = dotProduct * distanceFactor;

                            if (score > bestScore) {
                                bestScore = score;
                                bestPocket = pocket;
                            }
                        }
                    }
                });

                if (bestScore > 0.3) { // 只有当角度足够好时才显示
                    this.aimingSystem.pocketTarget = bestPocket;
                }
            }

            startAiming() {
                this.isAiming = true;
                this.power = 0;
                this.powerIncreasing = true;
                this.updatePowerMeter();
            }

            updatePowerMeter() {
                if (!this.isAiming) return;

                if (this.powerIncreasing) {
                    this.power += 2;
                    if (this.power >= this.maxPower) {
                        this.powerIncreasing = false;
                    }
                } else {
                    this.power -= 2;
                    if (this.power <= 0) {
                        this.powerIncreasing = true;
                    }
                }

                // 更新球杆后拉距离（根据力量值）
                this.cueStick.aimPower = (this.power / this.maxPower) * 50;
                this.updateCueStick();

                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = (this.power / this.maxPower * 100) + '%';

                requestAnimationFrame(() => this.updatePowerMeter());
            }

            shoot() {
                this.isAiming = false;

                if (!this.cueBall) return;

                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length > 0) {
                    const force = this.power * 0.5;
                    this.cueBall.vx = (dx / length) * force;
                    this.cueBall.vz = (dy / length) * force * 0.5; // Z轴移动较小
                }

                // 重置球杆状态
                this.cueStick.aimPower = 0;
                this.cueStick.visible = false;

                const powerBar = document.getElementById('powerBar');
                powerBar.style.height = '0%';

                // 延迟显示球杆，等球停下来
                setTimeout(() => {
                    this.updateCueStick();
                }, 500);
            }

            updatePhysics() {
                let anyBallMoving = false;

                // 更新球的位置
                this.balls.forEach(ball => {
                    ball.x += ball.vx;
                    ball.y += ball.vy;
                    ball.z += ball.vz;

                    // 摩擦力
                    ball.vx *= 0.98;
                    ball.vy *= 0.98;
                    ball.vz *= 0.98;

                    // 边界碰撞
                    if (Math.abs(ball.x) > 300) {
                        ball.vx = -ball.vx * 0.8;
                        ball.x = Math.sign(ball.x) * 300;
                    }
                    if (Math.abs(ball.z) > 150) {
                        ball.vz = -ball.vz * 0.8;
                        ball.z = Math.sign(ball.z) * 150;
                    }

                    // 检查球是否还在运动
                    if (Math.abs(ball.vx) > 0.5 || Math.abs(ball.vz) > 0.5) {
                        anyBallMoving = true;
                    } else {
                        ball.vx = 0;
                        ball.vz = 0;
                    }
                });

                // 如果有球在运动，隐藏球杆；如果都停止了，显示球杆
                if (anyBallMoving) {
                    this.cueStick.visible = false;
                } else if (!this.isAiming) {
                    this.updateCueStick();
                }

                // 球与球碰撞
                for (let i = 0; i < this.balls.length; i++) {
                    for (let j = i + 1; j < this.balls.length; j++) {
                        const ball1 = this.balls[i];
                        const ball2 = this.balls[j];

                        const dx = ball1.x - ball2.x;
                        const dz = ball1.z - ball2.z;
                        const distance = Math.sqrt(dx * dx + dz * dz);

                        if (distance < ball1.radius * 2) {
                            // 简单的碰撞响应
                            const angle = Math.atan2(dz, dx);
                            const sin = Math.sin(angle);
                            const cos = Math.cos(angle);

                            // 分离球
                            const overlap = ball1.radius * 2 - distance;
                            ball1.x += cos * overlap * 0.5;
                            ball1.z += sin * overlap * 0.5;
                            ball2.x -= cos * overlap * 0.5;
                            ball2.z -= sin * overlap * 0.5;

                            // 交换速度分量
                            const v1x = ball1.vx * cos + ball1.vz * sin;
                            const v1z = ball1.vz * cos - ball1.vx * sin;
                            const v2x = ball2.vx * cos + ball2.vz * sin;
                            const v2z = ball2.vz * cos - ball2.vx * sin;

                            ball1.vx = v2x * cos - v1z * sin;
                            ball1.vz = v1z * cos + v2x * sin;
                            ball2.vx = v1x * cos - v2z * sin;
                            ball2.vz = v2z * cos + v1x * sin;
                        }
                    }
                }
            }

            checkPockets() {
                const pockets = [
                    {x: -300, z: -150}, {x: 300, z: -150},
                    {x: -300, z: 150}, {x: 300, z: 150},
                    {x: 0, z: -150}, {x: 0, z: 150}
                ];

                this.balls = this.balls.filter(ball => {
                    for (let pocket of pockets) {
                        const dx = ball.x - pocket.x;
                        const dz = ball.z - pocket.z;
                        const distance = Math.sqrt(dx * dx + dz * dz);

                        if (distance < 30) {
                            if (ball.type === 'target') {
                                this.score += 10;
                                this.ballsLeft--;
                                this.updateUI();
                            } else if (ball.type === 'cue') {
                                setTimeout(() => this.resetCueBall(), 1000);
                            }
                            return false;
                        }
                    }
                    return true;
                });
            }

            resetCueBall() {
                this.cueBall = {
                    x: -150, y: 0, z: 0,
                    vx: 0, vy: 0, vz: 0,
                    radius: 12,
                    color: '#ffffff',
                    type: 'cue'
                };
                this.balls.push(this.cueBall);
            }

            updateUI() {
                document.getElementById('score').textContent = this.score;
                document.getElementById('ballsLeft').textContent = this.ballsLeft;
                document.getElementById('currentPlayer').textContent = `玩家${this.currentPlayer}`;

                if (this.ballsLeft === 0) {
                    alert('恭喜！游戏完成！总分：' + this.score);
                }
            }

            resetGame() {
                this.balls = [];
                this.score = 0;
                this.ballsLeft = 15;
                this.currentPlayer = 1;
                this.createBalls();
                this.updateUI();
            }

            toggleCamera() {
                // 切换相机角度
                if (this.camera.angleX > 0.2) {
                    this.camera.angleX = 0.1;
                    this.camera.y = -100;
                    this.camera.z = 200;
                } else {
                    this.camera.angleX = 0.3;
                    this.camera.y = -200;
                    this.camera.z = 300;
                }
            }

            render() {
                // 清空画布
                this.ctx.fillStyle = '#1a1a2e';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // 绘制桌面
                this.drawTable();

                // 绘制球（按Z轴排序以正确显示深度）
                const sortedBalls = [...this.balls].sort((a, b) => b.z - a.z);
                sortedBalls.forEach(ball => this.drawBall(ball));

                // 绘制球杆（在球后面）
                if (this.cueStick.visible && this.cueBall) {
                    this.drawCueStick();
                }

                // 绘制专业瞄准系统
                if (this.cueBall) {
                    this.drawProfessionalAiming();
                    if (this.isAiming) {
                        this.drawAimingHelper();
                    }
                }
            }

            drawTable() {
                // 桌面（3D效果）
                const corners = [
                    this.project3D(-300, 0, -150),
                    this.project3D(300, 0, -150),
                    this.project3D(300, 0, 150),
                    this.project3D(-300, 0, 150)
                ];

                this.ctx.fillStyle = '#0d5016';
                this.ctx.beginPath();
                this.ctx.moveTo(corners[0].x, corners[0].y);
                corners.forEach(corner => this.ctx.lineTo(corner.x, corner.y));
                this.ctx.closePath();
                this.ctx.fill();

                // 桌边
                this.ctx.strokeStyle = '#8B4513';
                this.ctx.lineWidth = 8;
                this.ctx.stroke();

                // 球袋
                const pockets = [
                    {x: -300, z: -150}, {x: 300, z: -150},
                    {x: -300, z: 150}, {x: 300, z: 150},
                    {x: 0, z: -150}, {x: 0, z: 150}
                ];

                this.ctx.fillStyle = '#000';
                pockets.forEach(pocket => {
                    const proj = this.project3D(pocket.x, 0, pocket.z);
                    this.ctx.beginPath();
                    this.ctx.arc(proj.x, proj.y, 20 * proj.scale, 0, Math.PI * 2);
                    this.ctx.fill();
                });
            }

            drawBall(ball) {
                const proj = this.project3D(ball.x, ball.y, ball.z);
                const radius = ball.radius * proj.scale;

                // 球的阴影
                this.ctx.fillStyle = 'rgba(0,0,0,0.3)';
                this.ctx.beginPath();
                this.ctx.arc(proj.x + 2, proj.y + 2, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 球体渐变
                const gradient = this.ctx.createRadialGradient(
                    proj.x - radius/3, proj.y - radius/3, 0,
                    proj.x, proj.y, radius
                );

                if (ball.type === 'cue') {
                    gradient.addColorStop(0, '#ffffff');
                    gradient.addColorStop(1, '#e0e0e0');
                } else {
                    gradient.addColorStop(0, ball.color);
                    gradient.addColorStop(0.7, ball.color);
                    gradient.addColorStop(1, '#333');
                }

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(proj.x, proj.y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 球的边框
                this.ctx.strokeStyle = '#333';
                this.ctx.lineWidth = 1;
                this.ctx.stroke();

                // 球号
                if (ball.type === 'target') {
                    this.ctx.fillStyle = '#fff';
                    this.ctx.font = `${Math.max(8, radius * 0.6)}px Arial`;
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText(ball.id.toString(), proj.x, proj.y + radius * 0.2);
                }
            }

            drawCueStick() {
                if (!this.cueStick.visible || !this.cueBall) return;

                // 投影球杆的起始和结束位置
                const stickStartProj = this.project3D(this.cueStick.x, this.cueStick.y, this.cueStick.z);

                // 计算球杆末端位置
                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length === 0) return;

                const normalizedX = dx / length;
                const normalizedY = dy / length;

                // 球杆末端位置（更远的位置）
                const stickEndX = stickStartProj.x - normalizedX * this.cueStick.length * stickStartProj.scale;
                const stickEndY = stickStartProj.y - normalizedY * this.cueStick.length * stickStartProj.scale;

                // 球杆粗细根据透视缩放
                const stickWidth = 8 * stickStartProj.scale;

                this.ctx.save();

                // 绘制球杆主体（木质部分）
                const gradient = this.ctx.createLinearGradient(
                    stickStartProj.x, stickStartProj.y,
                    stickEndX, stickEndY
                );
                gradient.addColorStop(0, '#8B4513');  // 深棕色（杆头）
                gradient.addColorStop(0.1, '#D2691E'); // 橙棕色
                gradient.addColorStop(0.7, '#DEB887'); // 浅棕色
                gradient.addColorStop(0.9, '#F4A460'); // 沙棕色
                gradient.addColorStop(1, '#8B4513');   // 深棕色（杆尾）

                this.ctx.strokeStyle = gradient;
                this.ctx.lineWidth = stickWidth;
                this.ctx.lineCap = 'round';
                this.ctx.beginPath();
                this.ctx.moveTo(stickStartProj.x, stickStartProj.y);
                this.ctx.lineTo(stickEndX, stickEndY);
                this.ctx.stroke();

                // 绘制球杆头（皮头）
                this.ctx.fillStyle = '#2F4F4F';
                this.ctx.beginPath();
                this.ctx.arc(stickStartProj.x, stickStartProj.y, stickWidth * 0.6, 0, Math.PI * 2);
                this.ctx.fill();

                // 绘制球杆装饰环
                const ringPositions = [0.15, 0.25, 0.35];
                ringPositions.forEach(pos => {
                    const ringX = stickStartProj.x + (stickEndX - stickStartProj.x) * pos;
                    const ringY = stickStartProj.y + (stickEndY - stickStartProj.y) * pos;

                    this.ctx.strokeStyle = '#654321';
                    this.ctx.lineWidth = stickWidth * 0.3;
                    this.ctx.beginPath();
                    this.ctx.arc(ringX, ringY, stickWidth * 0.4, 0, Math.PI * 2);
                    this.ctx.stroke();
                });

                // 绘制握把区域
                const gripStart = 0.6;
                const gripEnd = 0.9;
                const gripStartX = stickStartProj.x + (stickEndX - stickStartProj.x) * gripStart;
                const gripStartY = stickStartProj.y + (stickEndY - stickStartProj.y) * gripStart;
                const gripEndX = stickStartProj.x + (stickEndX - stickStartProj.x) * gripEnd;
                const gripEndY = stickStartProj.y + (stickEndY - stickStartProj.y) * gripEnd;

                const gripGradient = this.ctx.createLinearGradient(
                    gripStartX, gripStartY, gripEndX, gripEndY
                );
                gripGradient.addColorStop(0, '#8B4513');
                gripGradient.addColorStop(0.5, '#654321');
                gripGradient.addColorStop(1, '#8B4513');

                this.ctx.strokeStyle = gripGradient;
                this.ctx.lineWidth = stickWidth * 1.1;
                this.ctx.beginPath();
                this.ctx.moveTo(gripStartX, gripStartY);
                this.ctx.lineTo(gripEndX, gripEndY);
                this.ctx.stroke();

                // 绘制握把纹理
                for (let i = 0; i < 8; i++) {
                    const texturePos = gripStart + (gripEnd - gripStart) * (i / 8);
                    const textureX = stickStartProj.x + (stickEndX - stickStartProj.x) * texturePos;
                    const textureY = stickStartProj.y + (stickEndY - stickStartProj.y) * texturePos;

                    this.ctx.strokeStyle = '#4A4A4A';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.arc(textureX, textureY, stickWidth * 0.3, 0, Math.PI * 2);
                    this.ctx.stroke();
                }

                this.ctx.restore();
            }

            drawProfessionalAiming() {
                if (!this.cueBall || this.isAiming || !this.aimingSystem.showHelper) return;

                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);

                // 1. 绘制主瞄准线（从白球到鼠标）
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length > 0) {
                    // 主瞄准线 - 更粗更明显
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                    this.ctx.lineWidth = 3;
                    this.ctx.setLineDash([]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(cueBallProj.x, cueBallProj.y);

                    // 如果有目标球，线到目标球；否则到鼠标位置
                    if (this.aimingSystem.targetBall) {
                        const targetProj = this.project3D(
                            this.aimingSystem.targetBall.x,
                            this.aimingSystem.targetBall.y,
                            this.aimingSystem.targetBall.z
                        );
                        this.ctx.lineTo(targetProj.x, targetProj.y);
                    } else {
                        const aimLength = Math.min(length, 200);
                        const endX = cueBallProj.x + (dx / length) * aimLength;
                        const endY = cueBallProj.y + (dy / length) * aimLength;
                        this.ctx.lineTo(endX, endY);
                    }
                    this.ctx.stroke();
                }

                // 2. 绘制虚拟球（Ghost Ball）
                if (this.aimingSystem.ghostBall) {
                    const ghostProj = this.project3D(
                        this.aimingSystem.ghostBall.x,
                        0,
                        this.aimingSystem.ghostBall.z
                    );

                    // 虚拟白球位置
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([4, 4]);
                    this.ctx.beginPath();
                    this.ctx.arc(ghostProj.x, ghostProj.y, this.ballRadius * ghostProj.scale, 0, Math.PI * 2);
                    this.ctx.stroke();
                    this.ctx.setLineDash([]);
                }

                // 3. 高亮目标球
                if (this.aimingSystem.targetBall) {
                    const targetProj = this.project3D(
                        this.aimingSystem.targetBall.x,
                        this.aimingSystem.targetBall.y,
                        this.aimingSystem.targetBall.z
                    );

                    // 目标球光环
                    this.ctx.strokeStyle = 'rgba(255, 255, 0, 0.8)';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(targetProj.x, targetProj.y,
                               (this.ballRadius + 5) * targetProj.scale, 0, Math.PI * 2);
                    this.ctx.stroke();

                    // 撞击点标记
                    if (this.aimingSystem.hitPoint) {
                        const hitProj = this.project3D(
                            this.aimingSystem.hitPoint.x,
                            0,
                            this.aimingSystem.hitPoint.z
                        );

                        this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                        this.ctx.beginPath();
                        this.ctx.arc(hitProj.x, hitProj.y, 4 * hitProj.scale, 0, Math.PI * 2);
                        this.ctx.fill();
                    }
                }

                // 4. 绘制进袋路径
                if (this.aimingSystem.targetBall && this.aimingSystem.pocketTarget) {
                    const targetProj = this.project3D(
                        this.aimingSystem.targetBall.x,
                        this.aimingSystem.targetBall.y,
                        this.aimingSystem.targetBall.z
                    );
                    const pocketProj = this.project3D(
                        this.aimingSystem.pocketTarget.x,
                        0,
                        this.aimingSystem.pocketTarget.z
                    );

                    // 目标球到球袋的路径
                    this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.6)';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([6, 6]);
                    this.ctx.beginPath();
                    this.ctx.moveTo(targetProj.x, targetProj.y);
                    this.ctx.lineTo(pocketProj.x, pocketProj.y);
                    this.ctx.stroke();
                    this.ctx.setLineDash([]);

                    // 球袋高亮
                    this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.8)';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(pocketProj.x, pocketProj.y, 25 * pocketProj.scale, 0, Math.PI * 2);
                    this.ctx.stroke();
                }

                // 5. 瞄准精度指示器
                if (!this.aimingSystem.targetBall) {
                    // 如果没有目标球，显示自由瞄准模式
                    const aimLength = Math.min(length, 150);
                    const endX = cueBallProj.x + (dx / length) * aimLength;
                    const endY = cueBallProj.y + (dy / length) * aimLength;

                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    this.ctx.beginPath();
                    this.ctx.arc(endX, endY, 6, 0, Math.PI * 2);
                    this.ctx.fill();

                    // 十字准星
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.moveTo(endX - 10, endY);
                    this.ctx.lineTo(endX + 10, endY);
                    this.ctx.moveTo(endX, endY - 10);
                    this.ctx.lineTo(endX, endY + 10);
                    this.ctx.stroke();
                }
            }

            drawAimingHelper() {
                if (!this.cueBall) return;

                const cueBallProj = this.project3D(this.cueBall.x, this.cueBall.y, this.cueBall.z);
                const dx = this.mouse.x - cueBallProj.x;
                const dy = this.mouse.y - cueBallProj.y;
                const length = Math.sqrt(dx * dx + dy * dy);

                if (length > 0) {
                    // 力量指示器（围绕白球的圆圈）
                    const powerRadius = 20 + (this.power / this.maxPower) * 30;
                    this.ctx.strokeStyle = `rgba(255, ${255 - this.power * 2}, 0, 0.8)`;
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(cueBallProj.x, cueBallProj.y, powerRadius, 0, Math.PI * 2);
                    this.ctx.stroke();

                    // 预测轨迹
                    const force = this.power * 0.5;
                    const normalizedX = dx / length;
                    const normalizedY = dy / length;

                    let predX = this.cueBall.x;
                    let predZ = this.cueBall.z;
                    let predVx = normalizedX * force;
                    let predVz = normalizedY * force * 0.5;

                    this.ctx.strokeStyle = 'rgba(255, 255, 0, 0.5)';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([3, 3]);
                    this.ctx.beginPath();

                    const startProj = this.project3D(predX, 0, predZ);
                    this.ctx.moveTo(startProj.x, startProj.y);

                    // 模拟轨迹
                    for (let i = 0; i < 50; i++) {
                        predX += predVx * 2;
                        predZ += predVz * 2;
                        predVx *= 0.95;
                        predVz *= 0.95;

                        // 边界检测
                        if (Math.abs(predX) > 300 || Math.abs(predZ) > 150) break;
                        if (Math.abs(predVx) < 1 && Math.abs(predVz) < 1) break;

                        const proj = this.project3D(predX, 0, predZ);
                        this.ctx.lineTo(proj.x, proj.y);
                    }

                    this.ctx.stroke();
                    this.ctx.setLineDash([]);
                }
            }

            animate() {
                this.updatePhysics();
                this.checkPockets();
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }

        // 游戏初始化
        window.addEventListener('load', function() {
            try {
                window.game = new Enhanced3DPoolGame();
                console.log('3D桌球游戏加载成功！');
            } catch (error) {
                console.error('游戏初始化失败:', error);
                document.body.innerHTML = `
                    <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                color: white; text-align: center; font-size: 20px; background: rgba(0,0,0,0.8);
                                padding: 30px; border-radius: 10px;">
                        <h2>游戏加载失败</h2>
                        <p>错误信息: ${error.message}</p>
                        <p><a href="index_offline.html" style="color: #4CAF50;">点击这里使用离线版本</a></p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>