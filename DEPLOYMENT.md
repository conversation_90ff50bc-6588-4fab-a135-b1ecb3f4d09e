# 3D桌球游戏 - 部署指南

## 🚀 快速部署

### 本地运行
1. **直接运行**：双击 `index.html` 文件
2. **本地服务器**：
   ```bash
   # Python
   python -m http.server 8000
   
   # Node.js
   npx http-server
   
   # PHP
   php -S localhost:8000
   ```

### 网站部署

#### GitHub Pages
1. 将项目上传到GitHub仓库
2. 在仓库设置中启用GitHub Pages
3. 选择主分支作为源
4. 访问 `https://username.github.io/repository-name/`

#### Netlify
1. 将项目文件夹拖拽到 [Netlify](https://netlify.com)
2. 自动部署完成
3. 获得免费的HTTPS域名

#### Vercel
1. 在 [Vercel](https://vercel.com) 导入项目
2. 自动构建和部署
3. 支持自定义域名

## 📁 文件说明

### 必需文件
- `index.html` - 主游戏文件（推荐）
- `index_offline.html` - 备用版本

### 可选文件
- `README.md` - 项目说明
- `troubleshooting.md` - 故障排除
- `test.html` - 测试页面
- `js/game.js` - 原始文件（已弃用）

## 🔧 自定义配置

### 游戏参数调整
在 `index.html` 中可以调整以下参数：

```javascript
// 物理参数
ball.vx *= 0.98;  // 摩擦力（0.95-0.99）
force = this.power * 0.5;  // 击球力度（0.3-0.8）

// 视觉参数
this.perspective = 600;  // 透视强度（400-800）
this.ballRadius = 12;    // 球体大小（8-16）

// 桌面尺寸
const tableWidth = 600;   // 桌面宽度
const tableHeight = 300;  // 桌面高度
```

### 颜色主题
```javascript
// 背景色
this.ctx.fillStyle = '#1a1a2e';

// 桌面色
this.ctx.fillStyle = '#0d5016';

// 桌边色
this.ctx.strokeStyle = '#8B4513';
```

## 🌐 CDN部署

### 使用jsDelivr
```html
<!-- 如果需要外部访问 -->
<script src="https://cdn.jsdelivr.net/gh/username/repo@main/index.html"></script>
```

### 使用unpkg
```html
<!-- NPM包形式 -->
<script src="https://unpkg.com/your-package@latest/index.html"></script>
```

## 📱 移动端优化

### 响应式设计
游戏已支持移动端，但可以进一步优化：

```css
/* 移动端样式 */
@media (max-width: 768px) {
    #ui {
        font-size: 14px;
    }
    
    .button {
        padding: 8px 16px;
        font-size: 14px;
    }
    
    #powerMeter {
        width: 15px;
        height: 150px;
    }
}
```

### 触控支持
```javascript
// 添加触控事件
canvas.addEventListener('touchstart', handleTouchStart);
canvas.addEventListener('touchmove', handleTouchMove);
canvas.addEventListener('touchend', handleTouchEnd);
```

## 🔒 安全考虑

### HTTPS部署
- 现代浏览器要求HTTPS
- 使用免费SSL证书（Let's Encrypt）
- 云服务商通常提供免费HTTPS

### 内容安全策略
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; script-src 'self' 'unsafe-inline';">
```

## 📊 性能监控

### 基本监控
```javascript
// FPS监控
let lastTime = 0;
let frameCount = 0;

function measureFPS(currentTime) {
    frameCount++;
    if (currentTime - lastTime >= 1000) {
        console.log('FPS:', frameCount);
        frameCount = 0;
        lastTime = currentTime;
    }
}
```

### 错误追踪
```javascript
window.addEventListener('error', function(e) {
    console.error('游戏错误:', e.error);
    // 发送错误报告到服务器
});
```

## 🎯 SEO优化

### Meta标签
```html
<meta name="description" content="免费在线3D桌球游戏，无需下载，支持所有设备">
<meta name="keywords" content="桌球,台球,3D游戏,在线游戏,免费游戏">
<meta property="og:title" content="3D桌球游戏">
<meta property="og:description" content="体验真实的3D桌球游戏">
<meta property="og:image" content="screenshot.png">
```

### 结构化数据
```json
{
  "@context": "https://schema.org",
  "@type": "Game",
  "name": "3D桌球游戏",
  "description": "免费在线3D桌球游戏",
  "genre": "体育游戏",
  "playMode": "单人游戏"
}
```

## 🚀 部署检查清单

- [ ] 所有文件路径正确
- [ ] 在不同浏览器中测试
- [ ] 移动端兼容性测试
- [ ] 性能测试（FPS稳定）
- [ ] 错误处理测试
- [ ] HTTPS部署
- [ ] SEO优化
- [ ] 备用版本可用

---

**部署完成后，享受游戏吧！** 🎱
