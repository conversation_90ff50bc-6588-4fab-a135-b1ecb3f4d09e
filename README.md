# 3D桌球游戏

一个使用Three.js和Cannon.js构建的完整3D桌球游戏项目。

## 功能特性

### 🎮 游戏功能
- **真实3D物理引擎**：使用Cannon.js实现真实的球体碰撞和运动
- **完整桌球台**：包含标准桌球台、边界和6个球袋
- **16个球**：1个白球（主球）+ 15个彩球
- **瞄准系统**：鼠标控制瞄准方向
- **力量控制**：动态力量条，控制击球力度
- **得分系统**：进球得分，实时显示剩余球数
- **多视角**：支持切换不同相机视角

### 🎨 视觉效果
- **高质量渲染**：抗锯齿渲染，阴影效果
- **专业光照**：环境光 + 方向光 + 聚光灯组合
- **材质贴图**：不同球体颜色，木质桌边
- **流畅动画**：60FPS流畅游戏体验

### 🕹️ 操作控制
- **鼠标瞄准**：移动鼠标瞄准击球方向
- **点击击球**：按住鼠标左键蓄力，松开击球
- **力量显示**：右侧力量条实时显示击球力度
- **视角切换**：点击按钮切换不同观察角度

## 技术架构

### 前端技术栈
- **Three.js (r128)**：3D图形渲染引擎
- **Cannon.js (0.20.0)**：物理引擎
- **HTML5 Canvas**：渲染画布
- **ES6+ JavaScript**：现代JavaScript语法

### 核心模块

#### 1. 场景管理 (Scene Management)
```javascript
- 3D场景初始化
- 相机设置和控制
- 渲染器配置
- 光照系统
```

#### 2. 物理引擎 (Physics Engine)
```javascript
- 重力模拟
- 碰撞检测
- 摩擦力和弹性
- 材质属性
```

#### 3. 游戏对象 (Game Objects)
```javascript
- 桌球台和边界
- 球体（白球 + 彩球）
- 球袋
- 球杆
```

#### 4. 交互控制 (Interaction)
```javascript
- 鼠标事件处理
- 瞄准系统
- 力量控制
- 击球逻辑
```

#### 5. 游戏逻辑 (Game Logic)
```javascript
- 得分计算
- 球袋检测
- 游戏状态管理
- UI更新
```

## 文件结构

```
3d/
├── index.html          # 主页面文件
├── js/
│   └── game.js         # 游戏核心逻辑
└── README.md           # 项目说明文档
```

## 快速开始

### 1. 环境要求
- 现代浏览器（支持WebGL）
- 网络连接（加载CDN资源）

### 2. 运行游戏
1. 直接打开 `index.html` 文件
2. 或使用本地服务器：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx http-server
   ```
3. 在浏览器中访问游戏

### 3. 游戏操作
1. **瞄准**：移动鼠标指向目标方向
2. **击球**：按住鼠标左键蓄力，观察右侧力量条
3. **发射**：松开鼠标左键击球
4. **得分**：将彩球打入球袋获得分数
5. **重置**：点击"重新开始"按钮重置游戏

## 游戏规则

### 基本规则
- 使用白球击打彩球
- 将彩球打入球袋得分
- 每个彩球价值10分
- 白球进袋会重新放置
- 目标：将所有15个彩球打入球袋

### 操作技巧
- **力度控制**：观察力量条，适当控制击球力度
- **角度计算**：利用桌边反弹实现复杂击球
- **视角切换**：使用不同视角观察球台情况

## 扩展功能

### 可添加的功能
- [ ] 多人对战模式
- [ ] 球杆选择系统
- [ ] 音效和背景音乐
- [ ] 更多游戏模式（8球、9球等）
- [ ] 球的旋转效果
- [ ] 更精细的物理参数调节
- [ ] 移动端触控支持
- [ ] 在线排行榜

### 性能优化
- [ ] 对象池管理
- [ ] LOD（细节层次）系统
- [ ] 纹理压缩
- [ ] 渲染优化

## 技术细节

### 物理参数
```javascript
球体半径: 0.3
桌面尺寸: 16 × 8
重力加速度: -9.82
摩擦系数: 0.3
弹性系数: 0.7-0.9
```

### 渲染设置
```javascript
抗锯齿: 开启
阴影: PCF软阴影
视野角度: 75°
渲染距离: 0.1-1000
```

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ❌ Internet Explorer（不支持）

## 许可证

本项目仅供学习和演示使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受3D桌球游戏的乐趣！** 🎱