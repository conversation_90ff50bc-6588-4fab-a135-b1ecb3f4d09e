# 3D桌球游戏 - 修复版

一个完全可用的3D桌球游戏项目，无需外部依赖，开箱即用！

## ✅ 修复说明

**原版本问题**：
- 依赖外部CDN库（Three.js和Cannon.js），网络问题导致无法正常运行
- 3D引擎模拟对象不完整，缺少实际功能
- 混合的渲染方式导致游戏逻辑混乱

**修复后版本**：
- ✅ **完全自包含**：无需任何外部依赖，直接打开即可运行
- ✅ **真正的3D效果**：使用Canvas 2D实现伪3D透视投影
- ✅ **完整的物理引擎**：自实现的碰撞检测和物理模拟
- ✅ **流畅的游戏体验**：优化的渲染和物理计算

## 🎮 游戏功能

### 核心功能
- **伪3D视觉效果**：使用透视投影实现3D桌球台视觉
- **完整桌球台**：包含标准桌球台、边界和6个球袋
- **16个球**：1个白球（主球）+ 15个彩球
- **真实球杆效果**：高度仿真的球杆外观，包含木质纹理、装饰环、握把等细节
- **智能瞄准系统**：
  - 实时瞄准线显示
  - 球杆跟随鼠标移动
  - 蓄力时球杆后拉效果
  - 力量指示圆圈
  - 预测轨迹显示
- **力量控制**：动态力量条，控制击球力度
- **得分系统**：进球得分，实时显示剩余球数
- **多视角**：支持切换不同相机视角

### 🎨 视觉效果
- **专业台球桌**：
  - 真实的木质桌腿和边框
  - 高质量台呢纹理效果
  - 立体护栏和装饰线
  - 渐变阴影球袋设计
- **3D透视效果**：球体根据深度缩放，营造立体感
- **渐变球体**：每个球都有立体渐变效果和编号显示
- **阴影效果**：球体阴影增强立体感
- **真实球杆渲染**：
  - 木质纹理渐变效果
  - 金属杆头和装饰环
  - 皮质握把纹理
  - 透视缩放效果
- **智能相机动画**：
  - 平滑的视角过渡
  - 自动跟随球杆方向
  - 无突兀的视角切换
- **瞄准辅助视觉**：
  - 虚线瞄准线
  - 瞄准点标记
  - 力量指示圆圈（颜色渐变）
  - 预测轨迹线
- **流畅动画**：60FPS流畅游戏体验

### 🕹️ 操作控制
- **智能瞄准**：
  - 移动鼠标瞄准击球方向
  - 球杆实时跟随鼠标位置
  - 瞄准线动态显示
  - 专业瞄准辅助（H键切换）
- **智能相机**：
  - 自动跟随模式：相机根据球杆方向自动调整
  - 固定视角模式：传统的固定观察角度
  - 平滑过渡动画，无突兀切换
- **力量控制**：
  - 按住鼠标左键开始蓄力
  - 观察右侧力量条和球杆后拉距离
  - 力量指示圆圈颜色变化（绿→黄→红）
  - 预测轨迹实时显示
- **击球执行**：松开鼠标左键击球
- **视角切换**：点击按钮在自动跟随/固定视角间切换
- **自动隐藏**：球运动时球杆自动隐藏，停止后重新显示

## 技术架构

### 前端技术栈
- **HTML5 Canvas**：2D渲染画布
- **JavaScript ES6+**：现代JavaScript语法
- **自实现3D投影**：数学计算实现透视效果
- **自实现物理引擎**：碰撞检测和运动模拟

### 核心模块

#### 1. 3D投影系统 (3D Projection)
```javascript
- 透视投影计算
- 相机位置控制
- 深度排序渲染
- 视角切换
```

#### 2. 物理引擎 (Physics Engine)
```javascript
- 球体运动模拟
- 碰撞检测算法
- 摩擦力和弹性
- 边界反弹
```

#### 3. 游戏对象 (Game Objects)
```javascript
- 3D桌球台渲染
- 球体（白球 + 彩球）
- 球袋检测区域
- 瞄准线显示
```

#### 4. 交互控制 (Interaction)
```javascript
- 鼠标事件处理
- 瞄准系统
- 力量控制
- 击球逻辑
```

#### 5. 游戏逻辑 (Game Logic)
```javascript
- 得分计算
- 球袋检测
- 游戏状态管理
- UI更新
```

## 文件结构

```
3d/
├── index.html          # 主游戏文件（3D版本）
├── index_offline.html  # 备用版本（2D版本）
├── js/
│   └── game.js         # 原始游戏逻辑（已弃用）
├── README.md           # 项目说明文档
└── troubleshooting.md  # 故障排除指南
```

## 快速开始

### 1. 环境要求
- 现代浏览器（支持Canvas 2D）
- **无需网络连接**（完全离线可用）

### 2. 运行游戏
**方法1：直接运行**
1. 双击打开 `index.html` 文件
2. 游戏立即开始运行

**方法2：本地服务器**
```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用PHP
php -S localhost:8000
```

### 3. 备用版本
如果主版本有任何问题，可以使用：
- `index_offline.html` - 简化的2D版本

### 4. 游戏操作
1. **瞄准阶段**：
   - 移动鼠标指向目标方向
   - 观察白色虚线瞄准线和瞄准点
   - 球杆会自动跟随鼠标位置
2. **蓄力阶段**：
   - 按住鼠标左键开始蓄力
   - 观察右侧力量条变化
   - 球杆会根据力量后拉
   - 白球周围出现力量指示圆圈
   - 黄色虚线显示预测轨迹
3. **击球阶段**：
   - 松开鼠标左键击球
   - 球杆自动隐藏
   - 等待球停止后球杆重新出现
4. **得分**：将彩球打入球袋获得分数
5. **重置**：点击"重新开始"按钮重置游戏
6. **视角**：点击"切换视角"按钮改变观察角度

## 游戏规则

### 基本规则
- 使用白球击打彩球
- 将彩球打入球袋得分
- 每个彩球价值10分
- 白球进袋会重新放置
- 目标：将所有15个彩球打入球袋

### 操作技巧
- **精确瞄准**：
  - 利用瞄准线精确定位目标
  - 观察瞄准点位置调整角度
  - 球杆位置提供额外的视觉参考
- **力度控制**：
  - 观察力量条和力量圆圈颜色
  - 利用球杆后拉距离判断力度
  - 参考预测轨迹调整力量
- **高级技巧**：
  - 利用桌边反弹实现复杂击球
  - 观察预测轨迹规划路线
  - 使用不同视角观察球台情况
  - 等待球完全停止后再进行下一击

## 版本对比

### 主版本 (index.html)
- ✅ 伪3D透视效果
- ✅ 深度感和立体感
- ✅ 3D球杆渲染（透视缩放）
- ✅ 视角切换功能
- ✅ 更真实的视觉体验
- ✅ 完整的瞄准辅助系统

### 备用版本 (index_offline.html)
- ✅ 纯2D渲染
- ✅ 更好的兼容性
- ✅ 更低的性能要求
- ✅ 详细的2D球杆绘制
- ✅ 同样的瞄准辅助功能
- ✅ 适合低性能设备

## 技术特色

### 3D投影算法
```javascript
// 透视投影公式
scale = perspective / (perspective + z)
screenX = centerX + x * scale
screenY = centerY + y * scale
```

### 物理模拟
```javascript
// 球体碰撞检测
distance = sqrt((x1-x2)² + (z1-z2)²)
if (distance < radius1 + radius2) {
    // 执行碰撞响应
}
```

### 性能优化
- 深度排序渲染
- 高效的碰撞检测
- 优化的动画循环
- 智能的停止条件

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器
- ❌ Internet Explorer（不支持）

## 故障排除

如果遇到问题，请参考 `troubleshooting.md` 文件，或：

1. **游戏无法启动**：尝试使用 `index_offline.html`
2. **性能问题**：关闭其他浏览器标签页
3. **显示异常**：刷新页面或重启浏览器

## 更新日志

### v2.2 (专业台球桌版)
- ✅ **专业台球桌设计**：
  - 真实的桌腿结构
  - 木质边框和护栏
  - 台呢纹理效果
  - 立体球袋设计
- ✅ **智能相机系统**：
  - 根据球杆方向自动调整视角
  - 平滑的相机跟随动画
  - 固定视角/自动跟随模式切换
- ✅ **完整球桌视野**：解决只能看到一半球桌的问题
- ✅ **专业瞄准系统**：参考经典桌球游戏设计
- ✅ **视觉效果升级**：更真实的3D台球桌外观

### v2.1 (球杆增强版)
- ✅ **真实球杆渲染**：高度仿真的球杆外观设计
- ✅ **智能瞄准系统**：球杆跟随鼠标，实时瞄准反馈
- ✅ **蓄力视觉效果**：球杆后拉、力量圆圈、预测轨迹
- ✅ **自动隐藏机制**：球运动时隐藏球杆，停止后显示
- ✅ **瞄准辅助增强**：瞄准点、轨迹预测、力量指示
- ✅ **双版本同步**：主版本和备用版本功能一致

### v2.0 (修复版)
- ✅ 完全重写游戏引擎
- ✅ 移除外部依赖
- ✅ 实现真正的3D效果
- ✅ 优化物理模拟
- ✅ 改进用户体验

### v1.0 (原版)
- ❌ 依赖外部CDN
- ❌ 3D引擎不完整
- ❌ 网络问题导致无法运行

## 许可证

本项目仅供学习和演示使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**现在可以正常游玩3D桌球游戏了！** 🎱✨